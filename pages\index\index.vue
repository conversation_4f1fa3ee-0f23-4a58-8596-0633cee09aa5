<template>
	<view class="content">
		<!-- 顶部背景和标语部分 -->
		<view class="header">
			<image class="banner-image" src="/static/images/banner.png" mode="aspectFill"></image>
		</view>
		
		<!-- 选择站点部分 -->
		<view class="station-selector">
			<view class="station-item" @click="selectDeparture">
				<text class="station-name">{{departureStation}}</text>
			</view>
			<view class="switch-btn" @click="switchStations">
				<image :class="['switch-icon', {rotating: isRotating}]" src="/static/images/switch.png" mode="aspectFit"></image>
			</view>
			<view class="station-item" @click="selectArrival">
				<text class="station-name">{{arrivalStation}}</text>
			</view>
		</view>
		
		<!-- 日期选择部分 -->
		<view class="date-selector" @click="showDatePicker">
			<text class="date">{{date}}</text>
			<text class="week">{{week}}</text>
			<text class="today" v-if="isToday">今天</text>
		</view>
		
		<!-- 查询按钮 -->
		<view class="query-btn" @click="queryTickets">查询车票</view>
		
		<!-- 历史记录 -->
		<view class="history">
			<scroll-view class="history-scroll" scroll-x="true" show-scrollbar="false">
				<view class="history-scroll-content">
					<view class="history-item" v-for="(item, index) in historyList" :key="index" @click="useHistory(item)">
						<text class="history-text">{{item}}</text>
					</view>
				</view>
			</scroll-view>
			<view class="history-header">
				<text class="history-clear" @click="clearHistory">清除历史</text>
			</view>
		</view>
		
		<!-- uni-calendar日期选择器弹窗 -->
		<view class="calendar-popup" v-if="showCalendar">
			<view class="calendar-mask" @click="cancelDatePicker"></view>
			<view class="calendar-container">
				<view class="calendar-header custom-calendar-header">
					<text class="calendar-close" @click="cancelDatePicker">×</text>
					<text class="calendar-title">请选择日期</text>
				</view>
				
				<!-- 使用uni-calendar组件 -->
				<uni-calendar
					class="custom-calendar"
					ref="calendar"
					:insert="true"
					:lunar="false"
					:start-date="startDate"
					:end-date="endDate"
					@change="calendarChange"
					@monthSwitch="monthSwitch"
				/>
				
				<view class="calendar-footer">
					<view class="calendar-confirm" @click="confirmDatePicker">确认</view>
				</view>
			</view>
		</view>
		
		<!-- 站点选择弹窗 -->
		<view class="station-popup" v-if="showStationPicker">
			<view class="station-mask" @click="cancelStationPicker"></view>
			<view :class="['station-container', {'station-container-show': stationAnimateShow}]">
				<view class="station-header">
					<text class="station-close" @click="cancelStationPicker">×</text>
					<text class="station-title">{{isSelectingDeparture ? '出发地' : '到达地'}}</text>
				</view>
				
				<view class="station-tip">
					<text class="tip-icon">⚠</text>
					<text class="tip-text">提示：如有疑问，可在微信群中联系客服咨询～</text>
				</view>
				
				<view class="station-content">
					<!-- 左侧城市列表 -->
					<scroll-view class="city-list" scroll-y="true">
						<view 
							v-for="(city, index) in cities" 
							:key="index" 
							:class="['city-item', { 'city-item-active': currentCityIndex === index }]"
							@click="selectCity(index)"
						>
							<text class="city-name">{{city.addressName}}</text>
						</view>
					</scroll-view>
					
					<!-- 右侧站点列表 -->
					<scroll-view class="place-list" scroll-y="true">
						<view 
							v-for="(place, index) in currentCity.sysAddressDto" 
							:key="index" 
							:class="['place-item', { 'place-item-selected': isPlaceSelected(place) }]"
							@click="selectPlace(place)"
							v-if="currentCity.sysAddressDto && currentCity.sysAddressDto.length > 0"
						>
							<text class="place-name">{{place.addressName}}</text>
							<text v-if="isPlaceSelected(place)" class="place-check">✓</text>
						</view>
						<view v-if="!currentCity.sysAddressDto || currentCity.sysAddressDto.length === 0" class="no-place">
							<text class="no-place-text">暂无站点</text>
						</view>
					</scroll-view>
				</view>
				
				<view class="station-footer">
					<view class="station-confirm" @click="confirmStationPicker">确认</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import request from '../../utils/request.js';
	
	export default {
		data() {
			const now = new Date();
			const year = now.getFullYear();
			const month = now.getMonth() + 1;
			const day = now.getDate();
			const nowDateStr = `${year}-${month}-${day}`;
			
			// 设置最大日期为一年后
			const endYear = month === 12 ? year + 1 : year;
			const endMonth = month === 12 ? 1 : month + 1; 
			const endDateStr = `${endYear}-${endMonth}-${day}`;
			
			return {
				title: '购票系统',
				departureStation: '', // 初始值设为空，将从接口获取
				arrivalStation: '', // 初始值设为空，将从接口获取
				departureStationId: null, // 出发地ID
				arrivalStationId: null, // 到达地ID
				date: `${month}月${day}日`,
				week: `星期${['日', '一', '二', '三', '四', '五', '六'][now.getDay()]}`,
				isToday: true,
				historyList: [],
				isRotating: false,
				showCalendar: false,
				
				// 日历相关数据
				startDate: nowDateStr,
				endDate: `${year + 1}-${month}-${day}`,
				selectedDate: nowDateStr,
				selectedInfo: null,
				
				// 站点选择相关
				showStationPicker: false,
				isSelectingDeparture: true,
				tempStation: '',
				tempStationId: null, // 临时选中的站点ID
				currentCityIndex: 0,
				// 替换静态城市数据为空数组，将通过API加载
				departureCities: [],
				arrivalCities: [],
				cities: [], // 当前显示的城市列表（根据选择出发地/目的地动态切换）
				isLoading: false, // 加载状态
				stationAnimateShow: false
			}
		},
		computed: {
			currentCity() {
				return this.cities[this.currentCityIndex] || { addressName: '', sysAddressDto: [] };
			}
		},
		onLoad() {
			// 页面加载时获取历史记录和站点数据
			this.loadHistory();
			
			// 加载出发地和目的地数据
			this.loadDepartureStations();
			this.loadArrivalStations();
		},
		methods: {
			// 加载出发地数据
			loadDepartureStations() {
				this.isLoading = true;
				uni.showLoading({
					title: '加载中...'
				});
				
				request.get('/app/address/get?type=0').then(res => {
					if (res && res.code === 200 && res.data && res.data.length > 0) {
						this.departureCities = res.data;
						// 如果当前正在选择出发地，则更新显示的城市列表
						if (this.isSelectingDeparture) {
							this.cities = this.departureCities;
						}
						
						// 设置初始出发地（如果当前为空）
						if (!this.departureStation) {
							// 查找第一个有子站点的城市
							for (let city of this.departureCities) {
								if (city.sysAddressDto && city.sysAddressDto.length > 0) {
									this.departureStation = city.sysAddressDto[0].addressName;
									this.departureStationId = city.sysAddressDto[0].id;
									break;
								}
							}
							// 如果没有找到任何站点，使用第一个城市名称和ID
							if (!this.departureStation && this.departureCities.length > 0) {
								this.departureStation = this.departureCities[0].addressName;
								this.departureStationId = this.departureCities[0].id;
							}
						}
					} else {
						uni.showToast({
							title: '加载出发地数据失败',
							icon: 'none'
						});
					}
					this.isLoading = false;
					uni.hideLoading();
				}).catch(err => {
					console.error('加载出发地数据失败', err);
					uni.showToast({
						title: '加载出发地数据失败',
						icon: 'none'
					});
					this.isLoading = false;
					uni.hideLoading();
				});
			},
			
			// 加载目的地数据
			loadArrivalStations() {
				request.get('/app/address/get?type=1').then(res => {
					if (res && res.code === 200 && res.data && res.data.length > 0) {
						this.arrivalCities = res.data;
						// 如果当前正在选择目的地，则更新显示的城市列表
						if (!this.isSelectingDeparture) {
							this.cities = this.arrivalCities;
						}
						
						// 设置初始到达地（如果当前为空）
						if (!this.arrivalStation) {
							// 查找第一个有子站点的城市
							for (let city of this.arrivalCities) {
								if (city.sysAddressDto && city.sysAddressDto.length > 0) {
									this.arrivalStation = city.sysAddressDto[0].addressName;
									this.arrivalStationId = city.sysAddressDto[0].id;
									break;
								}
							}
							// 如果没有找到任何站点，使用第一个城市名称和ID
							if (!this.arrivalStation && this.arrivalCities.length > 0) {
								this.arrivalStation = this.arrivalCities[0].addressName;
								this.arrivalStationId = this.arrivalCities[0].id;
							}
						}
					} else {
						uni.showToast({
							title: '加载目的地数据失败',
							icon: 'none'
						});
					}
				}).catch(err => {
					console.error('加载目的地数据失败', err);
					uni.showToast({
						title: '加载目的地数据失败',
						icon: 'none'
					});
				});
			},
			
			// 加载历史记录
			loadHistory() {
				try {
					const history = uni.getStorageSync('searchHistory');
					if (history && Array.isArray(history)) {
						// 将历史记录转换为显示格式
						this.historyList = history.map(item => `${item.from}--${item.to}`);
					}
				} catch (e) {
					console.error('加载历史记录失败', e);
					this.historyList = [];
				}
			},
			
			// 日历组件日期变化事件
			calendarChange(e) {
				if (e.year && e.month && e.date) {
					// 保存选中的日期信息
					this.selectedInfo = e;
					// 构建完整的日期字符串
					this.selectedDate = `${e.year}-${e.month}-${e.date}`;
					
					// 判断是否为今天
					const now = new Date();
					const today = new Date(now.getFullYear(), now.getMonth(), now.getDate()).getTime();
					const selected = new Date(e.year, e.month - 1, e.date).getTime();
					this.isToday = (today === selected);
				}
			},
			
			// 日历组件月份切换事件
			monthSwitch(e) {
				console.log('月份切换', e);
			},
			
			// 选择出发站
			selectDeparture() {
				// 打开站点选择页面
				this.isSelectingDeparture = true;
				this.tempStation = this.departureStation;
				this.tempStationId = this.departureStationId;
				this.showStationPicker = true;

				// 设置当前显示的城市列表为出发地列表
				this.cities = this.departureCities;

				// 触发动画
				setTimeout(() => {
					this.stationAnimateShow = true;
				}, 50);

				// 根据当前站点设置选中的城市
				this.setInitialCityByStation(this.departureStation);
			},
			
			// 选择到达站
			selectArrival() {
				// 打开站点选择页面
				this.isSelectingDeparture = false;
				this.tempStation = this.arrivalStation;
				this.tempStationId = this.arrivalStationId;
				this.showStationPicker = true;

				// 设置当前显示的城市列表为目的地列表
				this.cities = this.arrivalCities;

				// 触发动画
				setTimeout(() => {
					this.stationAnimateShow = true;
				}, 50);

				// 根据当前站点设置选中的城市
				this.setInitialCityByStation(this.arrivalStation);
			},
			
			// 根据站点名称找到对应的城市索引
			setInitialCityByStation(stationName) {
				// 重置为默认选择第一个城市
				this.currentCityIndex = 0;
				
				// 遍历当前城市列表，查找包含该站点的城市
				for (let i = 0; i < this.cities.length; i++) {
					const city = this.cities[i];
					// 检查城市是否有子站点
					if (city.sysAddressDto && city.sysAddressDto.length > 0) {
						// 查找匹配的站点
						const found = city.sysAddressDto.some(place => place.addressName === stationName);
						if (found) {
							this.currentCityIndex = i;
							return;
						}
					}
				}
			},
			
			// 选择城市
			selectCity(index) {
				this.currentCityIndex = index;
			},
			
			// 选择站点
			selectPlace(place) {
				this.tempStation = place.addressName;
				this.tempStationId = place.id; // 记录选中站点的ID
			},
			
			// 判断站点是否被选中
			isPlaceSelected(place) {
				return this.tempStation === place.addressName;
			},
			
			// 确认站点选择
			confirmStationPicker() {
				if (this.tempStation && this.tempStationId) {
					if (this.isSelectingDeparture) {
						this.departureStation = this.tempStation;
						this.departureStationId = this.tempStationId;
					} else {
						this.arrivalStation = this.tempStation;
						this.arrivalStationId = this.tempStationId;
					}
				}
				this.closeStationPicker();
			},
			
			// 取消站点选择
			cancelStationPicker() {
				this.closeStationPicker();
			},
			
			// 关闭站点选择器（带动画）
			closeStationPicker() {
				this.stationAnimateShow = false;
				setTimeout(() => {
					this.showStationPicker = false;
				}, 300); // 等待动画完成
			},
			
			// 交换出发地和目的地
			switchStations() {
				const tempStation = this.departureStation;
				const tempStationId = this.departureStationId;

				this.departureStation = this.arrivalStation;
				this.departureStationId = this.arrivalStationId;
				this.arrivalStation = tempStation;
				this.arrivalStationId = tempStationId;

				// 触发旋转动画
				this.isRotating = true;
				setTimeout(() => {
					this.isRotating = false;
				}, 500); // 500ms后重置，与动画时间一致
			},
			
			// 显示日期选择器
			showDatePicker() {
				this.showCalendar = true;
			},
			
			// 取消日期选择
			cancelDatePicker() {
				this.showCalendar = false;
			},
			
			// 确认日期选择
			confirmDatePicker() {
				if (!this.selectedInfo) return;

				const { year, month, date } = this.selectedInfo;
				const selectedDate = new Date(year, month - 1, date);
				const weekDay = selectedDate.getDay();
				const weekDayNames = ['日', '一', '二', '三', '四', '五', '六'];

				this.date = `${month}月${date}日`;
				this.week = `星期${weekDayNames[weekDay]}`;
				this.isToday = this.isDateToday(selectedDate);

				// 更新selectedDate变量，用于传递给车票列表页面
				this.selectedDate = `${year}-${month}-${date}`;

				// 关闭日历弹窗
				this.showCalendar = false;
			},
			
			// 检查日期是否是今天
			isDateToday(date) {
				const today = new Date();
				return date.getDate() === today.getDate() && 
					   date.getMonth() === today.getMonth() && 
					   date.getFullYear() === today.getFullYear();
			},
			
			// 查询车票
			queryTickets() {
				// 验证出发地和目的地不能相同
				if(this.departureStation === this.arrivalStation) {
					uni.showToast({
						title: '出发地和目的地不能相同',
						icon: 'none'
					});
					return;
				}

				// 验证是否有地点ID
				if(!this.departureStationId || !this.arrivalStationId) {
					uni.showToast({
						title: '请重新选择出发地和目的地',
						icon: 'none'
					});
					return;
				}

				// 保存搜索历史
				const searchItem = {
					from: this.departureStation,
					to: this.arrivalStation,
					date: this.selectedDate,
					timestamp: new Date().getTime()
				};

				// 获取现有历史记录
				let searchHistory = uni.getStorageSync('searchHistory') || [];

				// 检查是否已存在相同的搜索记录（只基于出发地和目的地，不考虑日期）
				const existIndex = searchHistory.findIndex(item =>
					item.from === searchItem.from &&
					item.to === searchItem.to
				);

				// 如果存在，先从历史中删除
				if(existIndex > -1) {
					searchHistory.splice(existIndex, 1);
				}

				// 将新的搜索添加到历史开头
				searchHistory.unshift(searchItem);

				// 限制历史记录数量为10条
				if(searchHistory.length > 10) {
					searchHistory = searchHistory.slice(0, 10);
				}

				// 保存回本地存储
				uni.setStorageSync('searchHistory', searchHistory);

				// 更新页面显示的历史记录
				this.historyList = searchHistory.map(item => `${item.from}--${item.to}`);

				// 导航到车票查询结果页面，传递地点名称、ID和日期
				const params = {
					departure: encodeURIComponent(this.departureStation),
					arrival: encodeURIComponent(this.arrivalStation),
					upAddressId: this.departureStationId,
					downAddressId: this.arrivalStationId,
					selectedDate: this.selectedDate // 传递选择的日期
				};

				const queryString = Object.keys(params).map(key => `${key}=${params[key]}`).join('&');

				uni.navigateTo({
					url: `/subpkg-booking/ticket_list/ticket_list?${queryString}`
				});
			},
			
			// 使用历史记录
			useHistory(item) {
				const stations = item.split('--');
				if (stations.length === 2) {
					const fromStation = stations[0];
					const toStation = stations[1];

					// 设置出发地和目的地
					this.departureStation = fromStation;
					this.arrivalStation = toStation;

					// 查找对应的站点ID
					this.findStationId(fromStation, true); // 查找出发地ID
					this.findStationId(toStation, false); // 查找目的地ID
				}
			},

			// 根据站点名称查找站点ID
			findStationId(stationName, isDeparture) {
				const cities = isDeparture ? this.departureCities : this.arrivalCities;

				for (let city of cities) {
					if (city.sysAddressDto && city.sysAddressDto.length > 0) {
						for (let station of city.sysAddressDto) {
							if (station.addressName === stationName) {
								if (isDeparture) {
									this.departureStationId = station.id;
								} else {
									this.arrivalStationId = station.id;
								}
								return;
							}
						}
					}
					// 如果没有子站点，检查城市名称
					if (city.addressName === stationName) {
						if (isDeparture) {
							this.departureStationId = city.id;
						} else {
							this.arrivalStationId = city.id;
						}
						return;
					}
				}
			},
			
			// 清除历史记录
			clearHistory() {
				this.historyList = [];
				// 清除本地存储
				uni.removeStorageSync('searchHistory');
				uni.showToast({
					title: '历史记录已清除',
					icon: 'success'
				});
			}
		}
	}
</script>

<style scoped lang="scss">
	page {
		overflow: hidden;
		height: 100%;
	}
	
	.content {
		width: 100%;
		// height: 100vh;
		background-color: #ffffff;
		overflow: hidden;
		position: relative;
	}
	
	/* 顶部背景和标语 */
	.header {
		width: 100%;
		height: 400rpx;
		background: linear-gradient(to bottom, #a3c0f8, #d9e5fc);
		box-sizing: border-box;
	}
	
	.banner-image {
		width: 100%;
		height: 100%;
		object-fit: cover;
	}
	
	/* 选择站点 */
	.station-selector {
		width: 90%;
		margin: 20rpx auto;
		display: flex;
		justify-content: space-between;
		align-items: center;
	}
	
	.station-item {
		flex: 1;
		display: flex;
		justify-content: center;
	}

	.station-name {
		font-size: 36rpx;
		font-weight: bold;
		color: #333;
	}
	
	.switch-btn {
		width: 80rpx;
		height: 80rpx;
		display: flex;
		justify-content: center;
		align-items: center;
	}
	
	.switch-icon {
		width: 60rpx;
		height: 60rpx;
		transition: all 0.5s ease;
	}
	
	/* 添加旋转动画 */
	.rotating {
		animation: rotate360 0.5s linear;
	}
	
	@keyframes rotate360 {
		from {
			transform: rotate(0deg);
		}
		to {
			transform: rotate(360deg);
		}
	}
	
	/* 日期选择 */
	.date-selector {
		width: 90%;
		margin: 20rpx auto;
		display: flex;
		align-items: center;
		height: 80rpx;
		border-bottom: 1px solid #eee;
	}
	
	.date {
		font-size: 32rpx;
		color: #333;
		margin-right: 20rpx;
	}
	
	.week {
		font-size: 32rpx;
		color: #333;
		margin-right: 20rpx;
	}
	
	.today {
		font-size: 28rpx;
		color: #999;
		background-color: #f5f5f5;
		padding: 4rpx 16rpx;
		border-radius: 20rpx;
	}
	
	/* 查询按钮 */
	.query-btn {
		width: 90%;
		height: 90rpx;
		background: linear-gradient(to right, #3a97fa, #3b87f7);
		color: #fff;
		border-radius: 45rpx;
		margin: 30rpx auto;
		display: flex;
		justify-content: center;
		align-items: center;
		font-size: 36rpx;
	}
	
	/* 历史记录 */
	.history {
		width: 90%;
		margin: 20rpx auto;
		display: flex;
	}
	
	.history-header {
		display: flex;
		justify-content: flex-end;
		align-items: center;
		padding: 10rpx 10rpx;
	}
	
	.history-clear {
		font-size: 28rpx;
		color: #999;
	}
	
	.history-scroll {
		width: 80%;
		white-space: nowrap;
	}
	
	.history-scroll-content {
		display: inline-block;
		white-space: nowrap;
	}
	
	.history-item {
		display: inline-block;
		margin-right: 20rpx;
		padding: 8rpx 20rpx;
		background-color: #f5f5f5;
		border-radius: 30rpx;
	}
	
	.history-text {
		font-size: 28rpx;
		color: #666;
	}
	
	/* 日历弹窗 */
	.calendar-popup {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		z-index: 999;
	}
	
	.calendar-mask {
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background-color: rgba(0, 0, 0, 0.5);
	}
	
	.calendar-container {
		position: absolute;
		left: 0;
		right: 0;
		bottom: 0;
		background-color: #fff;
		border-radius: 20rpx 20rpx 0 0;
		padding: 30rpx;
	}
	
	.custom-calendar-header {
		display: flex;
		align-items: center;
		justify-content: center;
		position: relative;
		margin-bottom: 20rpx;
	}
	
	.calendar-close {
		position: absolute;
		left: 0;
		font-size: 48rpx;
		color: #333;
		line-height: 1;
	}
	
	.calendar-title {
		font-size: 34rpx;
		font-weight: bold;
	}
	
	.calendar-footer {
		margin-top: 20rpx;
		padding-bottom: 20rpx;
	}
	
	.calendar-confirm {
		height: 90rpx;
		background-color: #333;
		color: #fff;
		border-radius: 45rpx;
		display: flex;
		justify-content: center;
		align-items: center;
		font-size: 32rpx;
	}
	
	/* 站点选择器弹窗 */
	.station-popup {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		z-index: 999;
	}
	
	.station-mask {
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background-color: rgba(0, 0, 0, 0.5);
	}
	
	.station-container {
		position: absolute;
		top: 120rpx;
		left: 0;
		right: 0;
		bottom: 0;
		background-color: #fff;
		display: flex;
		flex-direction: column;
		border-radius: 20rpx 20rpx 0 0; /* 添加顶部圆角 */
		transform: translateY(100%); /* 初始位置在屏幕下方 */
		transition: transform 0.3s ease; /* 添加过渡效果 */
	}
	
	.station-container-show {
		transform: translateY(0); /* 显示状态 */
	}
	
	.station-header {
		display: flex;
		align-items: center;
		justify-content: center;
		position: relative;
		padding: 30rpx 0;
		border-bottom: 1px solid #f0f0f0;
	}
	
	.station-close {
		position: absolute;
		left: 30rpx;
		font-size: 48rpx;
		color: #333;
		line-height: 1;
	}
	
	.station-title {
		font-size: 36rpx;
		font-weight: bold;
	}
	
	.station-tip {
		background-color: #f0f8ff;
		padding: 20rpx 30rpx;
		display: flex;
		align-items: center;
	}
	
	.tip-icon {
		color: #FFB700;
		font-size: 32rpx;
		margin-right: 10rpx;
	}
	
	.tip-text {
		color: #666;
		font-size: 28rpx;
	}
	
	.station-content {
		display: flex;
		flex: 1;
		overflow: hidden;
	}
	
	.city-list {
		width: 200rpx;
		height: 100%;
		background-color: #f8f8f8;
	}
	
	.city-item {
		padding: 30rpx 20rpx;
		text-align: center;
	}
	
	.city-item-active {
		background-color: #fff;
		position: relative;
	}
	
	.city-item-active::before {
		content: '';
		position: absolute;
		left: 0;
		top: 30%;
		height: 40%;
		width: 8rpx;
		background-color: #3F8DF9;
	}
	
	.city-name {
		font-size: 28rpx;
		color: #333;
	}
	
	.place-list {
		flex: 1;
		height: 100%;
		padding: 0 30rpx;
	}
	
	.place-item {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 30rpx 0;
		border-bottom: 1px solid #f0f0f0;
	}
	
	.place-name {
		font-size: 32rpx;
		color: #333;
	}
	
	.place-item-selected .place-name {
		color: #3F8DF9;
	}
	
	.place-check {
		color: #3F8DF9;
		font-size: 32rpx;
		font-weight: bold;
	}
	
	.station-footer {
		padding: 20rpx 30rpx 40rpx;
	}
	
	.station-confirm {
		height: 90rpx;
		background-color: #333;
		color: #fff;
		border-radius: 45rpx;
		display: flex;
		justify-content: center;
		align-items: center;
		font-size: 32rpx;
	}
	
	/* 自定义uni-calendar样式 */
	.custom-calendar {
		--calendar-border-color: #f5f5f5;
		--calendar-text-color: #333;
		--calendar-lunar-color: #999;
		--calendar-background-color: #fff;
		--calendar-selected-background-color: #3F8DF9;
		--calendar-selected-lunar-color: #fff;
		--calendar-selected-text-color: #fff;
	}
	
	/* uni-calendar组件样式修改 */
	:deep(.uni-calendar) {
		background-color: #ffffff;
	}
	
	:deep(.uni-calendar__header) {
		display: none !important;
	}
	
	:deep(.uni-calendar__weeks) {
		padding: 10rpx 0;
	}
	
	:deep(.uni-calendar__weeks-day) {
		height: 90rpx;
	}
	
	:deep(.uni-calendar__weeks-day-text) {
		font-size: 30rpx;
		color: #333;
	}
	
	:deep(.uni-calendar__selected) {
		background-color: #3F8DF9;
		color: #fff;
		border-radius: 50%;
		width: 70rpx;
		height: 70rpx;
		line-height: 70rpx;
		text-align: center;
	}
	
	:deep(.uni-calendar__disabled) {
		color: #ccc;
		cursor: default;
	}
	
	:deep(.uni-calendar-item--disable) {
		color: #ccc;
		cursor: default;
	}
	
	:deep(.uni-calendar-item--before-checked), 
	:deep(.uni-calendar-item--after-checked) {
		background-color: rgba(63, 141, 249, 0.1);
		color: #333;
	}
	
	:deep(.uni-calendar__week-day) {
		height: 80rpx;
		line-height: 80rpx;
		text-align: center;
		font-size: 28rpx;
		color: #333;
	}
	
	:deep(.uni-calendar__week-day-text) {
		color: #333;
	}
	
	:deep(.uni-calendar__weeks-day-text) {
		color: #333;
	}
	
	:deep(.uni-calendar-item__weeks-box-circle) {
		border: 2rpx solid #3F8DF9;
		color: #3F8DF9;
		border-radius: 50%;
	}
	
	.no-place {
		padding: 50rpx 0;
		text-align: center;
	}
	
	.no-place-text {
		font-size: 28rpx;
		color: #999;
	}
</style>
