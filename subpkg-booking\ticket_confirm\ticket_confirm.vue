<template>
	<view class="content">
		<!-- 加载状态 -->
		<view v-if="loading" class="loading-container">
			<view class="loading-text">{{ loadingText }}</view>
		</view>

		<!-- 行程信息 -->
		<view v-else class="trip-info">
			<view class="date-time">{{ date }} {{ dateDesc }} {{ time }}出发 (约{{ duration }}小时)</view>

			<view class="route-info">
				<view class="route-stations">
					<text class="route-from">{{ departure }}-{{ departureDoor }}</text>
					<text class="route-arrow">→</text>
					<text class="route-to">{{ arrival }}</text>
				</view>

				<view class="bus-type">{{ typeName }}</view>
			</view>

			<view class="trip-details">
				<view class="trip-detail-item" @click="showAddressDetail">
					<view class="detail-info">
						<text class="detail-label">ⓘ 发车前24小时不可退票、改签</text>
						<!-- <text class="detail-separator">|</text> -->
						<text class="detail-label" style="text-align: right;padding-right: 10rpx;">{{ departureLocation }}</text>
					</view>
				</view>
			</view>
		</view>

		<!-- 乘客选择 -->
		<view class="passenger-section">
			<view class="section-title">选择乘客</view>

			<view class="passenger-list">
				<view v-for="(passenger, index) in passengerList" :key="index" class="passenger-item"
					@click="selectPassenger(index)">
					<!-- 选择框 -->
					<view class="passenger-checkbox">
						<view :class="['checkbox-circle', { 'checkbox-active': passenger.selected }]">
							<view v-if="passenger.selected" class="checkbox-inner"></view>
						</view>
					</view>

					<!-- 乘客信息（合并在一行） -->
					<view class="passenger-info">
						<text class="passenger-name">{{ passenger.name }}</text>
						<text class="passenger-id">{{ passenger.idCard }}</text>
						<text class="passenger-type-text">{{ passenger.type }}</text>
					</view>

					<!-- 右箭头图标 -->
					<view class="passenger-arrow" @click.stop="editPassenger(index)">
						<image src="/static/icons/arrow_right.png" class="arrow-icon"></image>
					</view>
				</view>

				<!-- 选择乘客按钮 -->
				<view class="add-passenger" @click="goToPassengerManagement">
					<image src="/static/icons/add_circle.png" class="add-icon"></image>
					<text class="add-text">选择乘客</text>
				</view>
			</view>

			<!-- 乘客电话 -->
			<view class="passenger-phone">
				<view class="phone-label">乘客电话 <text class="required"></text></view>
				<input
					class="phone-input"
					type="number"
					placeholder="请输入联系电话"
					v-model="contactPhone"
					maxlength="11"
				/>
			</view>

			<!-- 备注信息 -->
			<view class="remark-section">
				<view class="remark-label">备注</view>
				<input
					class="remark-input"
					placeholder="有其它特定要求请备注（选填）"
					v-model="remark"
				/>
			</view>
		</view>

		<!-- 价格信息 -->
		<view class="price-section">
			<view class="price-item">
				<text class="price-label">车票总价</text>
				<text class="price-value">¥{{ totalPrice.toFixed(2) }}</text>
			</view>

			<view class="price-item">
				<text class="price-label">厂商优惠</text>
				<text class="price-discount">减¥{{ discount.toFixed(2) }}</text>
			</view>

			<view class="price-item coupon-item" @click="selectCoupon">
				<text class="price-label">优惠券</text>
				<view class="coupon-select">
					<text class="coupon-text" v-if="!selectedCoupon">选择优惠券</text>
					<text class="coupon-selected" v-else>-¥{{ couponDiscount.toFixed(2) }}</text>
					<image src="/static/icons/arrow_right.png" class="arrow-icon"></image>
				</view>
			</view>

			<view class="total-price">
				<text class="total-label">合计</text>
				<text class="total-value">¥{{ finalPrice.toFixed(2) }}</text>
			</view>
		</view>

		<!-- 底部支付栏 -->
		<view class="payment-bar">
			<!-- 购票须知 -->
			<view class="terms-section">
				<view class="terms-checkbox" @click="toggleTerms">
					<view :class="['checkbox-circle', { 'checkbox-active': termsAgreed }]">
						<view v-if="termsAgreed" class="checkbox-inner"></view>
					</view>
				</view>
				<text class="terms-text">我已阅读并同意</text>
				<text class="terms-link" @click.stop="viewTerms">《购票须知》</text>
			</view>
			<view class="payment-total">
				<text class="payment-label">共计</text>
				<text class="payment-currency">¥</text>
				<text class="payment-value">{{ finalPrice.toFixed(2) }}</text>
			</view>

			<view class="payment-button" @click="confirmPayment">立即支付</view>
		</view>
		
		<!-- 优惠券选择弹窗 -->
		<view class="coupon-popup" v-if="showCouponPopup">
			<view class="coupon-mask" @click="closeCouponPopup"></view>
			<view class="coupon-container">
				<view class="coupon-header">
					<text class="coupon-close" @click="closeCouponPopup">×</text>
					<text class="coupon-title">使用优惠券</text>
				</view>
				
				<view class="coupon-list">
					<view v-for="(coupon, index) in couponList" 
						:key="coupon.id" 
						class="coupon-item"
						@click="chooseCoupon(index)">
						<!-- 优惠券内容 -->
						<view class="coupon-content">
							<!-- 左侧金额 -->
							<view class="coupon-amount">
								<text class="coupon-currency">¥</text>
								<text class="coupon-value">{{ coupon.amount }}</text>
								<text v-if="coupon.condition" class="coupon-condition">{{ coupon.condition }}</text>
							</view>
							<!-- 右侧信息 -->
							<view class="coupon-info">
								<text class="coupon-type">{{ coupon.type }}</text>
								<text class="coupon-expire">有效期至：{{ coupon.expireDate }}</text>
							</view>
							<!-- 选择状态 -->
							<view class="coupon-select-icon" :class="{'selected': coupon.selected}">
								<view v-if="coupon.selected" class="select-inner"></view>
							</view>
						</view>
						<!-- 锯齿边 -->
						<view class="coupon-edge-left"></view>
						<view class="coupon-edge-right"></view>
					</view>
				</view>
				
				<view class="coupon-footer">
					<view class="coupon-confirm-btn" @click="confirmCoupon">确认</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
import { ticketApi, configApi } from '@/utils/api.js';
import auth from '@/utils/auth.js';
import request from '@/utils/request.js';

export default {
	data() {
		return {
			// 车票ID
			ticketId: null,

			// 加载状态
			loading: false,
			loadingText: '加载中...',

			// 路线信息
			departure: '',
			departureDoor: '',
			arrival: '',
			date: '',
			dateDesc: '',
			time: '',
			duration: '',
			typeName: '',
			type: '',
			departureLocation: '',

			// 价格信息
			originalPrice: 0,
			currentPrice: 0,
			discount: 0,
			finalPrice: 0,

			// 乘客信息
			passengerList: [], // 改为空数组，从乘客管理页面选择
			contactPhone: '', // 联系电话（必填）
			remark: '', // 备注信息（选填）

			// 计算后的价格
			totalPrice: 100,

			// 条款同意
			termsAgreed: false,
			
			// 优惠券相关
			showCouponPopup: false, // 是否显示优惠券弹窗
			couponList: [], // 从接口获取的优惠券列表
			selectedCoupon: null, // 选中的优惠券
			couponDiscount: 0 // 优惠券优惠金额
		};
	},

	onLoad(options) {
		// 从缓存获取用户手机号
		this.loadUserContactInfo();

		// 加载优惠券列表
		this.loadCouponList();

		// 从URL参数获取车票ID
		if (options && options.ticketId) {
			this.ticketId = options.ticketId;

			// 先设置基本信息（从URL参数获取）
			this.departure = decodeURIComponent(options.departure || '');
			this.arrival = decodeURIComponent(options.arrival || '');
			this.date = decodeURIComponent(options.date || '');
			this.dateDesc = decodeURIComponent(options.dateDesc || '');

			// 设置导航栏标题
			if (this.departure && this.arrival) {
				uni.setNavigationBarTitle({
					title: `${this.departure} — ${this.arrival}`
				});
			}

			// 调用接口获取车票详细信息
			this.loadTicketDetail();
		} else {
			// 如果没有车票ID，使用备用数据或默认值
			this.loadBackupData(options);
		}
	},

	methods: {
		// 从缓存加载用户联系信息
		loadUserContactInfo() {
			try {
				const userInfo = auth.getUserInfo();
				if (userInfo && userInfo.phone) {
					// 可以预填充用户手机号，但用户仍需确认
					this.contactPhone = userInfo.phone;
					console.log('从缓存预填充用户手机号:', this.contactPhone);
				} else {
					console.warn('缓存中未找到用户手机号信息，需要用户手动输入');
					this.contactPhone = '';
				}
			} catch (error) {
				console.error('获取用户联系信息失败:', error);
				this.contactPhone = '';
			}
		},

		// 加载优惠券列表
		async loadCouponList() {
			try {
				console.log('开始加载优惠券列表...');
				const response = await request.get('/app/coupon/user/list');

				if (response && response.code === 200 && response.data) {
					// 处理优惠券数据，只保留待使用的无门槛优惠券
					this.couponList = this.processCouponsData(response.data);
					console.log('优惠券列表加载成功:', this.couponList);
				} else {
					console.warn('优惠券列表加载失败:', response);
					this.couponList = [];
				}
			} catch (error) {
				console.error('加载优惠券列表失败:', error);
				this.couponList = [];
			}
		},

		// 处理优惠券数据
		processCouponsData(data) {
			return data.filter(item => {
				// 只保留待使用的优惠券 (type === 0)
				return item.type === 0;
			}).map(item => {
				// 格式化时间
				const endTime = this.formatDate(item.endTime);

				return {
					id: item.id,
					amount: item.balancePice,
					title: item.couponName || '优惠券',
					type: '无门槛', // 写死为无门槛类型
					expireDate: endTime,
					selected: false,
					condition: '',
					couponId: item.couponId
				};
			});
		},

		// 格式化日期
		formatDate(dateStr) {
			if (!dateStr) return '';

			try {
				const date = new Date(dateStr);
				const year = date.getFullYear();
				const month = String(date.getMonth() + 1).padStart(2, '0');
				const day = String(date.getDate()).padStart(2, '0');
				const hour = String(date.getHours()).padStart(2, '0');
				const minute = String(date.getMinutes()).padStart(2, '0');
				return `${year}.${month}.${day} ${hour}:${minute}`;
			} catch (error) {
				console.error('日期格式化失败:', error);
				return dateStr;
			}
		},

		// 调用计算价格接口
		async calculatePriceFromApi() {
			try {
				// 计算选中的乘客数量
				const selectedCount = this.passengerList.filter(item => item.selected).length;

				if (selectedCount === 0) {
					console.log('没有选中的乘客，跳过价格计算');
					// 重置价格信息
					this.totalPrice = 0;
					this.discount = 0;
					this.finalPrice = 0;
					return;
				}

				console.log('selectedCoupon', this.selectedCoupon);
				const requestData = {
					ticketId: this.ticketId,
					number: selectedCount,
					couponId: this.selectedCoupon ? this.selectedCoupon.couponId : null
				};

				console.log('调用计算价格接口，参数:', requestData);

				const response = await request.post('/app/order/calculate/price', requestData);

				if (response && response.code === 200 && response.data) {
					console.log('价格计算接口响应:', response);

					// 更新价格信息
					const data = response.data;
					this.totalPrice = data.originalPrice || 0; // 车票总价
					this.discount = data.discountPrice || 0; // 厂商优惠
					this.finalPrice = data.number || 0; // 合计金额

					// 计算优惠券优惠金额（总价 - 厂商优惠 - 合计 = 优惠券优惠）
					this.couponDiscount = this.totalPrice - this.discount - this.finalPrice;
					if (this.couponDiscount < 0) {
						this.couponDiscount = 0;
					}

					console.log('价格更新完成:', {
						totalPrice: this.totalPrice,
						discount: this.discount,
						couponDiscount: this.couponDiscount,
						finalPrice: this.finalPrice
					});
				} else {
					console.warn('价格计算接口调用失败:', response);
				}
			} catch (error) {
				console.error('调用价格计算接口失败:', error);
			}
		},

		// 加载车票详细信息
		async loadTicketDetail() {
			try {
				this.loading = true;
				this.loadingText = '正在加载车票信息...';

				console.log('获取车票详情，ID:', this.ticketId);

				// 调用接口获取车票详细信息
				const response = await ticketApi.getTicketById(this.ticketId);

				if (response && response.data) {
					// 处理接口返回的数据
					this.processTicketDetail(response.data);
				} else {
					throw new Error('获取车票信息失败');
				}

			} catch (error) {
				console.error('获取车票详情失败:', error);
				uni.showToast({
					title: '获取车票信息失败',
					icon: 'none'
				});

				// 如果接口调用失败，尝试使用备用数据
				this.loadBackupDataFromUrl();
			} finally {
				this.loading = false;
			}
		},

		// 处理车票详细信息
		processTicketDetail(data) {
			// 使用与ticket_list页面相同的时间提取方法
			this.time = this.extractTimeFromDateTime(data.departureTime);
			this.typeName = data.carType;
			this.type = this.getTicketType(data.vehicleType);
			this.departureDoor = data.departureDoor ;
			this.duration = data.travelTime;
			this.originalPrice = parseFloat(data.originalPrice || 0);
			this.currentPrice = parseFloat(data.currentPrice || data.price || 0);
			this.discount = parseFloat(data.discount || 0);
			this.departureLocation = data.departureLocation;

			// 计算价格
			this.calculatePrice();

			console.log('车票详情处理完成:', {
				time: this.time,
				typeName: this.typeName,
				originalPrice: this.originalPrice,
				currentPrice: this.currentPrice
			});
		},

		// 从完整的日期时间字符串中提取时分（与ticket_list页面保持一致）
		extractTimeFromDateTime(dateTimeString) {
			if (!dateTimeString) {
				return '08:00'; // 默认时间
			}

			try {
				let timeStr = '';

				if (dateTimeString.includes(' ')) {
					// 格式: "2025-06-10 00:00:00"
					timeStr = dateTimeString.split(' ')[1];
				} else if (dateTimeString.includes('T')) {
					// 格式: "2025-06-10T00:00:00"
					timeStr = dateTimeString.split('T')[1];
				} else {
					// 如果已经是时间格式，直接返回
					return dateTimeString;
				}

				// 提取时分部分 (HH:MM)
				if (timeStr && timeStr.length >= 5) {
					return timeStr.substring(0, 5); // 取前5位 "HH:MM"
				}

				return '08:00'; // 默认时间
			} catch (error) {
				console.error('解析发车时间失败:', dateTimeString, error);
				return '08:00'; // 默认时间
			}
		},

		// 根据车辆类型获取对应的样式类型（与ticket_list页面保持一致）
		getTicketType(vehicleType) {
			switch (vehicleType) {
				case 1:
				case '商务车':
					return 'business';
				case 2:
				case '普通车':
				default:
					return 'normal';
			}
		},

		// 加载备用数据（当没有车票ID时使用）
		loadBackupData(options) {
			// 默认值
			const defaults = {
				departure: '同济大学',
				departureDoor: '南门',
				arrival: '泰安各县城',
				date: '05月14日',
				dateDesc: '明天',
				time: '07:50',
				duration: '约3小时',
				typeName: '商务车',
				type: 'business',
				originalPrice: 100.00,
				currentPrice: 98.00,
				discount: 2.00
			};

			// 使用URL参数或默认值
			if (options && Object.keys(options).length > 0) {
				this.departure = decodeURIComponent(options.departure || defaults.departure);
				this.departureDoor = decodeURIComponent(options.departureDoor || defaults.departureDoor);
				this.arrival = decodeURIComponent(options.arrival || defaults.arrival);
				this.date = decodeURIComponent(options.date || defaults.date);
				this.dateDesc = decodeURIComponent(options.dateDesc || defaults.dateDesc);
				this.time = decodeURIComponent(options.time || defaults.time);
				this.duration = decodeURIComponent(options.duration || defaults.duration);
				this.typeName = decodeURIComponent(options.typeName || defaults.typeName);
				this.type = options.type || defaults.type;
				this.originalPrice = parseFloat(options.originalPrice) || defaults.originalPrice;
				this.currentPrice = parseFloat(options.currentPrice) || defaults.currentPrice;
				this.discount = parseFloat(options.discount) || defaults.discount;
			} else {
				// 使用默认值
				Object.assign(this, defaults);
			}

			// 设置导航栏标题
			uni.setNavigationBarTitle({
				title: `${this.departure} — ${this.arrival}`
			});

			// 计算价格
			this.calculatePrice();
		},

		// 从URL参数加载备用数据（当接口调用失败时使用）
		loadBackupDataFromUrl() {
			// 尝试从当前页面的URL参数中获取备用数据
			const pages = getCurrentPages();
			const currentPage = pages[pages.length - 1];
			const options = currentPage.options;

			if (options) {
				this.departureDoor = decodeURIComponent(options.departureDoor || '南门');
				this.time = decodeURIComponent(options.time || '08:00');
				this.duration = decodeURIComponent(options.duration || '约6小时');
				this.typeName = decodeURIComponent(options.typeName || '普通车');
				this.type = options.type || 'normal';
				this.originalPrice = parseFloat(options.originalPrice) || 100;
				this.currentPrice = parseFloat(options.currentPrice) || 98;
				this.discount = parseFloat(options.discount) || 2;

				// 计算价格
				this.calculatePrice();
			}
		},

		// 显示地址详情
		showAddressDetail() {
			// 实现显示地址详情的逻辑
		},

		// 选择乘客
		selectPassenger(index) {
			// 如果有乘客数据，切换选中状态
			if (this.passengerList.length > 0) {
				this.passengerList[index].selected = !this.passengerList[index].selected;
				this.calculatePrice();
				// 调用价格计算接口
				this.calculatePriceFromApi();
			} else {
				// 如果没有乘客数据，跳转到乘客管理页面
				this.goToPassengerManagement();
			}
		},

		// 跳转到乘客管理页面
		goToPassengerManagement() {
			uni.navigateTo({
				url: '/subpkg-user/passenger_management/passenger_management'
			});
		},

		// 编辑乘客信息
		editPassenger(index) {
			// 获取要编辑的乘客信息
			const passenger = this.passengerList[index];

			// 跳转到编辑乘客页面
			uni.navigateTo({
				url: '/subpkg-user/passenger_edit/passenger_edit',
				success: (res) => {
					// 传递乘客数据
					res.eventChannel.emit('acceptPassengerData', {
						index: index,
						passenger: passenger
					});
				}
			});
		},

		// 添加乘客
		addPassenger() {
			// 跳转到编辑乘客页面，不传递乘客数据表示新增
			uni.navigateTo({
				url: '/subpkg-user/passenger_edit/passenger_edit'
			});
		},

		// 添加新乘客到列表（供编辑页面调用）
		addNewPassenger(passenger) {
			this.passengerList.push(passenger);
			// 重新计算价格
			this.calculatePrice();
			// 调用价格计算接口
			this.calculatePriceFromApi();
		},

		// 更新已有乘客（供编辑页面调用）
		updatePassenger(index, passenger) {
			if (index >= 0 && index < this.passengerList.length) {
				this.passengerList[index] = passenger;
				// 重新计算价格
				this.calculatePrice();
				// 调用价格计算接口
				this.calculatePriceFromApi();
			}
		},

		// 移除乘客（供编辑页面调用）
		removePassenger(index) {
			if (index >= 0 && index < this.passengerList.length) {
				this.passengerList.splice(index, 1);
				// 重新计算价格
				this.calculatePrice();
				// 调用价格计算接口
				this.calculatePriceFromApi();
			}
		},

		// 接收从乘客管理页面选择的乘客
		receiveSelectedPassengers(selectedPassengers) {
			// 将选中的乘客添加到乘客列表中
			selectedPassengers.forEach(passenger => {
				// 检查是否已存在（根据ID或身份证号判断）
				const exists = this.passengerList.some(p =>
					p.id === passenger.id || p.idCard === passenger.idCard
				);

				if (!exists) {
					// 添加乘客，设置为选中状态
					this.passengerList.push({
						...passenger,
						selected: true,
						type: '成人票' // 默认票型
					});
				}
			});

			// 重新计算价格
			this.calculatePrice();
			// 调用价格计算接口
			this.calculatePriceFromApi();

			// 提示用户
			uni.showToast({
				title: `已选择${selectedPassengers.length}位乘客`,
				icon: 'success'
			});
		},

		// 选择优惠券
		selectCoupon() {
			// 显示优惠券弹窗
			this.showCouponPopup = true;
			
			// 初始化选中的优惠券
			this.selectedCoupon = this.couponList.find(item => item.selected) || null;
		},

		// 关闭优惠券弹窗
		closeCouponPopup() {
			this.showCouponPopup = false;
		},

		// 选择某个优惠券
		chooseCoupon(index) {
			const currentCoupon = this.couponList[index];

			// 如果当前优惠券已经选中，则取消选择
			if (currentCoupon.selected) {
				currentCoupon.selected = false;
				this.selectedCoupon = null;
			} else {
				// 取消所有优惠券的选中状态
				this.couponList.forEach(item => {
					item.selected = false;
				});

				// 设置当前优惠券为选中状态
				currentCoupon.selected = true;
				this.selectedCoupon = currentCoupon;
			}

			console.log('选择的优惠券:', this.selectedCoupon);
		},

		// 确认选择优惠券
		confirmCoupon() {
			// 如果没有选择优惠券，重置优惠券相关数据
			if (!this.selectedCoupon) {
				this.couponDiscount = 0;
			}

			// 调用价格计算接口（接口会返回实际的优惠金额）
			this.calculatePriceFromApi();

			// 关闭弹窗
			this.closeCouponPopup();
		},

		// 切换条款同意状态
		toggleTerms() {
			this.termsAgreed = !this.termsAgreed;
		},

		// 查看购票须知
		async viewTerms() {
			try {
				// 显示加载中
				uni.showLoading({
					title: '加载中...'
				});

				// 调用配置接口获取购票须知内容
				const response = await configApi.getConfig('bay_ticket');

				uni.hideLoading();

				if (response && response.code === 200) {
					// 使用弹窗展示购票须知内容
					uni.showModal({
						title: '购票须知',
						content: response.msg || '暂无内容',
						showCancel: false,
						confirmText: '我知道了'
					});
				} else {
					uni.showToast({
						title: '获取购票须知失败',
						icon: 'none'
					});
				}
			} catch (error) {
				uni.hideLoading();
				console.error('获取购票须知失败:', error);
				uni.showToast({
					title: '获取购票须知失败',
					icon: 'none'
				});
			}
		},

		// 确认支付
		async confirmPayment() {
			// 验证条款同意
			if (!this.termsAgreed) {
				uni.showToast({
					title: '请阅读并同意《购票须知》',
					icon: 'none'
				});
				return;
			}

			// 检查是否选择了乘客
			const selectedPassengers = this.passengerList.filter(item => item.selected);
			if (selectedPassengers.length === 0) {
				uni.showToast({
					title: '请选择乘车人',
					icon: 'none'
				});
				return;
			}

			// 验证联系电话（必填）
			if (!this.contactPhone || this.contactPhone.trim() === '') {
				uni.showToast({
					title: '请输入联系电话',
					icon: 'none'
				});
				return;
			}

			// 验证电话格式
			const phoneRegex = /^1[3-9]\d{9}$/;
			if (!phoneRegex.test(this.contactPhone)) {
				uni.showToast({
					title: '请输入正确的手机号码',
					icon: 'none'
				});
				return;
			}

			try {
				uni.showLoading({
					title: '正在创建订单...',
					mask: true
				});

				// 调用订单创建接口
				const orderData = {
					ticketId: this.ticketId,
					couponId: this.selectedCoupon ? this.selectedCoupon.id : null,
					phone: parseInt(this.contactPhone), // 转换为Long类型
					remark: this.remark || '', // 备注信息，可为空
					sysPassengerInformations: selectedPassengers.map(passenger => ({
						id: passenger.id,
						name: passenger.name,
						idNumber: passenger.rawIdNumber || passenger.idCard,
						// 如果有其他字段，可以在这里添加
					}))
				};

				console.log('创建订单请求参数:', orderData);

				const response = await request.post('/app/order', orderData);

				if (response && response.code === 200) {
					console.log('订单创建成功:', response);

					// 检查是否有支付参数
					if (response.data && response.data.appid) {
						uni.showLoading({
							title: '正在发起支付...',
							mask: true
						});

						// 调用微信支付
						this.callWechatPayWithParams(response.data);
					} else {
						uni.hideLoading();
						uni.showToast({
							title: '订单创建成功',
							icon: 'success'
						});
					}
				} else {
					throw new Error(response.msg || '订单创建失败');
				}
			} catch (error) {
				uni.hideLoading();
				console.error('订单创建失败:', error);
				uni.showToast({
					title: error.message || '订单创建失败，请重试',
					icon: 'none'
				});
			}
		},

		// 使用后端返回的完整参数调用微信支付
		callWechatPayWithParams(payData) {
			uni.hideLoading();

			console.log('调用微信支付，参数:', payData);

			// 构建支付参数
			const paymentParams = {
				provider: 'wxpay',
				appid: payData.appid,
				timeStamp: payData.timeStamp,
				nonceStr: payData.nonceStr,
				package: payData.prepayId, // 注意：这里使用prepayId字段
				signType: payData.signType,
				paySign: payData.paySign
			};

			console.log('微信支付参数:', paymentParams);

			// 调用微信支付
			uni.requestPayment({
				...paymentParams,
				success: (res) => {
					console.log('支付成功:', res);
					uni.showToast({
						title: '支付成功',
						icon: 'success'
					});

					// 跳转到购票记录页面，并传递支付成功标识
					setTimeout(() => {
						uni.reLaunch({
							url: '/pages/record/record?paymentSuccess=true'
						});
					}, 1500);
				},
				fail: (err) => {
					console.error('支付失败:', err);
					let errorMsg = '支付失败，请重试';

					if (err.errMsg) {
						if (err.errMsg.includes('cancel')) {
							errorMsg = '支付已取消';
						} else if (err.errMsg.includes('fail')) {
							errorMsg = '支付失败，请检查网络或重试';
						}
					}

					uni.showToast({
						title: errorMsg,
						icon: 'none'
					});
				}
			});
		},

		// 调用微信支付（旧方法，保留用于测试）
		callWechatPay(packageValue) {
			// 生成时间戳（秒）
			const timeStamp = Math.floor(Date.now() / 1000).toString();

			// 生成随机字符串
			const nonceStr = this.generateNonceStr();

			// 小程序appid
			const appId = 'wx01c1e1d1bfcd4145';

			uni.hideLoading();

			console.log('调用微信支付，参数:', {
				appId: appId,
				timeStamp: timeStamp,
				nonceStr: nonceStr,
				package: packageValue,
				signType: 'MD5'
			});

			// 调用微信支付
			uni.requestPayment({
				provider: 'wxpay',
				"appid": "",
				timeStamp: "",
				nonceStr: "",
				package: "",
				signType: '',
				paySign: '', 
				success: (res) => {
					console.log('支付成功:', res);
					uni.showToast({
						title: '支付成功',
						icon: 'success'
					});

					// 跳转到购票记录页面，并传递支付成功标识
					setTimeout(() => {
						uni.reLaunch({
							url: '/pages/record/record?paymentSuccess=true'
						});
					}, 1500);
				},
				fail: (err) => {
					console.error('支付失败:', err);
					let errorMsg = '支付失败，请重试';

					if (err.errMsg) {
						if (err.errMsg.includes('cancel')) {
							errorMsg = '支付已取消';
						} else if (err.errMsg.includes('fail')) {
							errorMsg = '支付失败，请检查网络或重试';
						}
					}

					uni.showToast({
						title: errorMsg,
						icon: 'none'
					});
				}
			});
		},

		// 生成随机字符串
		generateNonceStr() {
			const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
			let result = '';
			for (let i = 0; i < 32; i++) {
				result += chars.charAt(Math.floor(Math.random() * chars.length));
			}
			return result;
		},

		// 计算价格（本地计算，主要用于初始化和备用）
		calculatePrice() {
			// 计算选中的乘客数量
			const selectedCount = this.passengerList.filter(item => item.selected).length;

			// 如果没有选中乘客，重置价格
			if (selectedCount === 0) {
				this.totalPrice = 0;
				this.discount = 0;
				this.couponDiscount = 0;
				this.finalPrice = 0;
				return;
			}

			// 简单的本地计算（作为备用，主要依赖接口返回的数据）
			this.totalPrice = this.originalPrice * selectedCount;
			this.discount = 2.00 * selectedCount;

			// 考虑优惠券优惠（使用实际计算出的优惠金额）
			const finalPrice = this.totalPrice - this.discount - this.couponDiscount;
			this.finalPrice = finalPrice > 0 ? finalPrice : 0;
		}
	}
}
</script>

<style lang="scss" scoped>
page {
	background-color: #F5F5F5;
}

.content {
	// min-height: 100vh;
	display: flex;
	flex-direction: column;
	padding-bottom: 180rpx;
	/* 为底部的购票须知和支付栏预留空间 */
	// padding-top: 10rpx;
	background-color: #f5f7fa;
	/* 添加顶部边距 */
}

/* 加载状态 */
.loading-container {
	display: flex;
	justify-content: center;
	align-items: center;
	padding: 200rpx 0;
	min-height: 50vh;
}

.loading-text {
	font-size: 28rpx;
	color: #999;
}

/* 行程信息样式 */
.trip-info {
	background-color: #fff;
	padding: 34rpx 24rpx;
	margin-bottom: 24rpx;
	// border-radius: 16rpx;
	// margin: 24rpx;
	// box-shadow: 0 6rpx 20rpx rgba(0, 0, 0, 0.1), 0 2rpx 5rpx rgba(0, 0, 0, 0.1);
	// border: 1rpx solid rgba(0, 0, 0, 0.03);
}

.date-time {
	font-size: 24rpx;
	// font-weight: bold;
	color: #303133;
	padding-bottom: 20rpx;
	// border-bottom: 1px solid #f0f0f0;
}

.route-info {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 30rpx 0;
	border-bottom: 1px solid #f0f0f0;
}

.route-stations {
	display: flex;
	align-items: center;
}

.route-from,
.route-to {
	font-size: 32rpx;
	color: #333;
	font-weight: bold;
}

.route-arrow {
	margin: 0 10rpx;
	color: #999;
}

.bus-type {
	font-size: 26rpx;
	color: #000000;
	// background-color: #f5f7fa;
	padding: 6rpx 16rpx;
	font-weight: bold;
	// border-radius: 6rpx;
}

.trip-details {
	padding-top: 20rpx;
}

.trip-detail-item {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 16rpx 0;
}

.detail-info {
	display: flex;
	align-items: center;
	flex-wrap: wrap;
	width: 100%;
}

.detail-label {
	width: 50%;
	font-size: 24rpx;
	color: #303133;
	white-space: nowrap;
	overflow: hidden;
	box-sizing: border-box;
}

.detail-separator {
	font-size: 22rpx;
	color: #ddd;
	margin: 0 10rpx;
	padding-top: 2rpx;
}

.arrow-icon {
	width: 32rpx;
	height: 32rpx;
}

/* 乘客选择样式 */
.passenger-section {
	background-color: #fff;
	padding: 34rpx 24rpx;
	margin-bottom: 24rpx;
	border-radius: 16rpx;
	margin: 0 24rpx 24rpx 24rpx;
	// box-shadow: 0 6rpx 20rpx rgba(0, 0, 0, 0.1), 0 2rpx 5rpx rgba(0, 0, 0, 0.1);
	// border: 1rpx solid rgba(0, 0, 0, 0.03);
}

.section-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 20rpx;
}

.passenger-item {
	display: flex;
	align-items: center;
	padding: 20rpx 0;
	border-bottom: 1px solid #f0f0f0;
}

.passenger-checkbox {
	margin-right: 20rpx;
}

.checkbox-circle {
	width: 40rpx;
	height: 40rpx;
	border-radius: 50%;
	border: 2rpx solid #ddd;
	display: flex;
	align-items: center;
	justify-content: center;
}

.checkbox-active {
	border-color: #3F8DF9;
}

.checkbox-inner {
	width: 24rpx;
	height: 24rpx;
	border-radius: 50%;
	background-color: #3F8DF9;
}

.passenger-info {
	flex: 1;
	display: flex;
	flex-direction: row;
	align-items: center;
	flex-wrap: nowrap;
	overflow: hidden;
}

.passenger-name {
	font-size: 30rpx;
	color: #333;
	font-weight: bold;
	margin-right: 10rpx;
	white-space: nowrap;
}

.passenger-id {
	font-size: 26rpx;
	color: #999;
	margin-right: 20rpx;
	white-space: nowrap;
}

.passenger-type-text {
	font-size: 24rpx;
	color: #666;
	background-color: #f5f7fa;
	padding: 4rpx 12rpx;
	border-radius: 4rpx;
	white-space: nowrap;
}

.passenger-arrow {
	width: 40rpx;
	height: 40rpx;
	display: flex;
	align-items: center;
	justify-content: center;
}

.add-passenger {
	display: flex;
	align-items: center;
	padding: 30rpx 0;
	justify-content: center;
	background: #F7F8FA;
}

.add-icon {
	width: 40rpx;
	height: 40rpx;
	margin-right: 10rpx;
}

.add-text {
	font-size: 30rpx;
	color: #3F8DF9;
}

.passenger-phone {
	display: flex;
	// flex-direction: column;
	padding: 20rpx 0;
	border-top: 1px solid #f0f0f0;
	align-items: center;
}

.phone-label {
	font-size: 28rpx;
	color: #303133;
	margin-right: 10rpx;
}

.required {
	color: #ff4757;
	font-size: 28rpx;
}

.phone-input {
	height: 80rpx;
	padding: 0 20rpx;
	border: none;
	border-radius: 8rpx;
	font-size: 28rpx;
	color: #333;
	background-color: #fff;
}

.phone-input:focus {
	border-color: #3F8DF9;
}

.phone-value {
	font-size: 28rpx;
	color: #333;
}

/* 备注信息样式 */
.remark-section {
	display: flex;
	// flex-direction: column;
	padding: 20rpx 0;
	border-top: 1px solid #f0f0f0;
	align-items: center;
}

.remark-label {
	font-size: 28rpx;
	color: #303133;
	margin-right: 10rpx;
}

.remark-input {
	padding: 20rpx;
	border: none;
	border-radius: 8rpx;
	font-size: 28rpx;
	color: #333;
	background-color: #fff;
	resize: none;
	width: 80%;
}

.remark-input:focus {
	border-color: #3F8DF9;
}

/* 价格信息样式 */
.price-section {
	background-color: #fff;
	padding: 34rpx 24rpx;
	margin-bottom: 24rpx;
	border-radius: 16rpx;
	margin: 0 24rpx 24rpx 24rpx;
	// box-shadow: 0 6rpx 20rpx rgba(0, 0, 0, 0.1), 0 2rpx 5rpx rgba(0, 0, 0, 0.1);
	// border: 1rpx solid rgba(0, 0, 0, 0.03);
}

.price-item {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 10rpx 0;
}

.price-label {
	font-size: 28rpx;
	color: #333;
}

.price-value {
	font-size: 28rpx;
	color: #333;
}

.price-discount {
	font-size: 28rpx;
	color: #EE0A24;
}

.coupon-item {
	padding: 20rpx 0;
	border-top: 1px solid #f0f0f0;
	border-bottom: 1px solid #f0f0f0;
}

.coupon-select {
	display: flex;
	align-items: center;
}

.coupon-text {
	font-size: 28rpx;
	color: #999;
	margin-right: 10rpx;
}

.total-price {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 20rpx 0 0;
}

.total-label {
	font-size: 30rpx;
	font-weight: bold;
	color: #333;
}

.total-value {
	font-size: 30rpx;
	font-weight: bold;
	color: #333;
}

/* 购票须知样式 */
.terms-section {
	display: flex;
	align-items: center;
	justify-content: center;
	padding: 20rpx;
	position: fixed;
	bottom: 100rpx;
	left: 0;
	right: 0;
	background-color: #fff;
	border-top: 1rpx solid #f0f0f0;
	z-index: 9;
}

.terms-checkbox {
	margin-right: 10rpx;
}

.terms-text {
	font-size: 26rpx;
	color: #666;
}

.terms-link {
	font-size: 26rpx;
	color: #3F8DF9;
}

/* 底部支付栏样式 */
.payment-bar {
	position: fixed;
	left: 0;
	right: 0;
	bottom: 0;
	background-color: #fff;
	display: flex;
	align-items: center;
	padding: 20rpx 30rpx;
	box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
	z-index: 10;
	border-top: 1rpx solid #f0f0f0;
}

.payment-total {
	flex: 1;
	display: flex;
	align-items: baseline;
}

.payment-label {
	font-size: 26rpx;
	color: #666;
	margin-right: 10rpx;
}

.payment-currency {
	font-size: 30rpx;
	color: #EE0A24;
	font-weight: bold;
	margin-right: 4rpx;
}

.payment-value {
	font-size: 42rpx;
	color: #EE0A24;
	font-weight: bold;
}

.payment-button {
	background-color: #3F8DF9;
	color: #fff;
	font-size: 32rpx;
	padding: 20rpx 60rpx;
	border-radius: 50rpx;
}

/* 优惠券已选择状态样式 */
.coupon-selected {
	font-size: 28rpx;
	color: #ff9500;
	font-weight: bold;
	margin-right: 10rpx;
}

/* 优惠券选择弹窗样式 */
.coupon-popup {
	position: fixed;
	left: 0;
	right: 0;
	top: 0;
	bottom: 0;
	z-index: 11;
}

.coupon-mask {
	position: absolute;
	left: 0;
	right: 0;
	top: 0;
	bottom: 0;
	background-color: rgba(0, 0, 0, 0.5);
}

.coupon-container {
	position: fixed;
	left: 0;
	right: 0;
	bottom: 0;
	background-color: #fff;
	border-radius: 24rpx 24rpx 0 0;
	z-index: 12;
	animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
	from {
		transform: translateY(100%);
	}
	to {
		transform: translateY(0);
	}
}

.coupon-header {
	display: flex;
	align-items: center;
	justify-content: center;
	padding: 30rpx 0;
	position: relative;
	border-bottom: 1px solid #f5f5f5;
}

.coupon-close {
	position: absolute;
	left: 30rpx;
	font-size: 46rpx;
	color: #333;
	line-height: 1;
}

.coupon-title {
	font-size: 34rpx;
	font-weight: bold;
	color: #333;
}

.coupon-list {
	max-height: 60vh;
	overflow-y: auto;
	padding: 30rpx 20rpx;
}

.coupon-item {
	position: relative;
	margin-bottom: 30rpx;
}

.coupon-content {
	background-color: #FF7744;
	padding: 30rpx 20rpx;
	border-radius: 12rpx;
	display: flex;
	align-items: center;
	position: relative;
}

.coupon-amount {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	width: 200rpx;
	padding-right: 20rpx;
	position: relative;
}

.coupon-amount::after {
	content: '';
	position: absolute;
	top: -20rpx;
	bottom: -20rpx;
	right: 0;
	width: 2rpx;
	background-color: rgba(255, 255, 255, 0.3);
}

.coupon-currency {
	font-size: 36rpx;
	color: #fff;
	font-weight: bold;
	margin-right: 4rpx;
}

.coupon-value {
	font-size: 80rpx;
	color: #fff;
	font-weight: bold;
	line-height: 1;
}

.coupon-condition {
	font-size: 24rpx;
	color: rgba(255, 255, 255, 0.9);
	margin-top: 10rpx;
}

.coupon-info {
	flex: 1;
	padding-left: 30rpx;
	display: flex;
	flex-direction: column;
	justify-content: center;
}

.coupon-type {
	font-size: 36rpx;
	color: #fff;
	font-weight: bold;
	margin-bottom: 20rpx;
}

.coupon-expire {
	font-size: 28rpx;
	color: rgba(255, 255, 255, 0.9);
}

.coupon-select-icon {
	width: 44rpx;
	height: 44rpx;
	border-radius: 50%;
	background-color: #fff;
	display: flex;
	align-items: center;
	justify-content: center;
	position: absolute;
	right: 30rpx;
	top: 50%;
	transform: translateY(-50%);
	border: 2rpx solid transparent;
}

.selected .select-inner {
	width: 28rpx;
	height: 28rpx;
	border-radius: 50%;
	background-color: #3F8DF9;
}

/* 优惠券锯齿边 */
.coupon-edge-left, .coupon-edge-right {
	position: absolute;
	top: 50%;
	width: 30rpx;
	height: 30rpx;
	border-radius: 50%;
	background-color: #f5f7fa;
}

.coupon-edge-left {
	left: -15rpx;
	transform: translateY(-50%);
}

.coupon-edge-right {
	right: -15rpx;
	transform: translateY(-50%);
}

.coupon-footer {
	padding: 20rpx 30rpx 50rpx;
}

.coupon-confirm-btn {
	background-color: #333;
	color: #fff;
	font-size: 34rpx;
	padding: 24rpx;
	border-radius: 50rpx;
	text-align: center;
}
</style>