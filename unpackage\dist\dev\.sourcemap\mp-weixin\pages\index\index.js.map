{"version": 3, "sources": ["uni-app:///main.js", "webpack:///E:/购票系统/购票系统/pages/index/index.vue?0b50", "webpack:///E:/购票系统/购票系统/pages/index/index.vue?64c8", "webpack:///E:/购票系统/购票系统/pages/index/index.vue?2477", "webpack:///E:/购票系统/购票系统/pages/index/index.vue?65cd", "uni-app:///pages/index/index.vue", "webpack:///E:/购票系统/购票系统/pages/index/index.vue?7fed"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "renderjs", "component", "options", "__file", "components", "uniCalendar", "e", "message", "indexOf", "console", "error", "render", "_vm", "this", "_h", "$createElement", "l0", "_self", "_c", "showStationPicker", "__map", "currentCity", "sysAddressDto", "place", "index", "$orig", "__get_orig", "g0", "length", "m0", "isPlaceSelected", "m1", "g1", "$mp", "data", "Object", "assign", "$root", "recyclableRender", "staticRenderFns", "_withStripped", "title", "departureStation", "arrivalStation", "departureStationId", "arrivalStationId", "date", "week", "isToday", "historyList", "isRotating", "showCalendar", "startDate", "endDate", "selectedDate", "selectedInfo", "isSelectingDeparture", "tempStation", "tempStationId", "currentCityIndex", "departureCities", "arrivalCities", "cities", "isLoading", "stationAnimateShow", "computed", "addressName", "onLoad", "methods", "loadDepartureStations", "uni", "request", "icon", "loadArrivalStations", "loadHistory", "calendarChange", "monthSwitch", "selectDeparture", "setTimeout", "selectArrival", "setInitialCityByStation", "selectCity", "selectPlace", "confirmStationPicker", "cancelStationPicker", "closeStationPicker", "switchStations", "showDatePicker", "cancelDatePicker", "confirmDatePicker", "year", "month", "isDateToday", "queryTickets", "from", "to", "timestamp", "item", "searchHistory", "departure", "arrival", "upAddressId", "downAddressId", "url", "useHistory", "findStationId", "city", "clearHistory"], "mappings": "2IAAA,MAGA,aACA,WAFAA,EAAGC,kCAAoCC,EAGvCC,EAAWC,a,+ECLX,iIACIC,EADJ,QASIC,EAAY,qBACd,aACA,YACA,sBACA,EACA,KACA,WACA,MACA,EACA,gBACAD,GAGFC,EAAUC,QAAQC,OAAS,wBACZ,aAAAF,E,yCCvBf,sQ,gCCAA,IAAIG,EAAJ,0LACA,IACEA,EAAa,CACXC,YAAa,WACX,OAAO,kIAKX,MAAOC,GACP,IAC+C,IAA7CA,EAAEC,QAAQC,QAAQ,wBACa,IAA/BF,EAAEC,QAAQC,QAAQ,QAWlB,MAAMF,EATNG,QAAQC,MAAMJ,EAAEC,SAChBE,QAAQC,MAAM,mBACdD,QAAQC,MACN,uFAEFD,QAAQC,MACN,mDAMN,IAAIC,EAAS,WACX,IAAIC,EAAMC,KACNC,EAAKF,EAAIG,eAETC,GADKJ,EAAIK,MAAMC,GACVN,EAAIO,kBACTP,EAAIQ,MAAMR,EAAIS,YAAYC,eAAe,SAAUC,EAAOC,GACxD,IAAIC,EAAQb,EAAIc,WAAWH,GACvBI,EACFf,EAAIS,YAAYC,eAChBV,EAAIS,YAAYC,cAAcM,OAAS,EACrCC,EAAKF,EAAKf,EAAIkB,gBAAgBP,GAAS,KACvCQ,EAAKJ,EAAKf,EAAIkB,gBAAgBP,GAAS,KAC3C,MAAO,CACLE,MAAOA,EACPE,GAAIA,EACJE,GAAIA,EACJE,GAAIA,MAGR,MACAC,EAAKpB,EAAIO,mBACRP,EAAIS,YAAYC,eACwB,IAAzCV,EAAIS,YAAYC,cAAcM,OAC9B,KACJhB,EAAIqB,IAAIC,KAAOC,OAAOC,OACpB,GACA,CACEC,MAAO,CACLrB,GAAIA,EACJgB,GAAIA,MAKRM,GAAmB,EACnBC,EAAkB,GACtB5B,EAAO6B,eAAgB,G,gCC9DvB,wHAAsqB,eAAG,G,0HC6HzqB,kiCAEA,CACAN,gBACA,eACA,kBACA,iBACA,cACA,2CAGA,eACA,eACA,yCAEA,OACAO,aACAC,oBACAC,kBACAC,wBACAC,sBACAC,oCACAC,4DACAC,WACAC,eACAC,cACAC,gBAGAC,YACAC,mDACAC,eACAC,kBAGApC,qBACAqC,wBACAC,eACAC,mBACAC,mBAEAC,mBACAC,iBACAC,UACAC,aACAC,wBAGAC,UACA5C,uBACA,4CAAA6C,eAAA5C,oBAGA6C,kBAEA,mBAGA,6BACA,4BAEAC,SAEAC,iCAAA,WACA,kBACAC,eACA7B,iBAGA8B,2DACA,6CAQA,GAPA,yBAEA,yBACA,6BAIA,oBACA,IACA,EADA,IACA,iEACA,8CACA,kDACA,2CACA,QAGA,+BACA,iDACA,oDACA,oDAIAD,aACA7B,kBACA+B,cAGA,eACAF,mBACA,mBACA7D,6BACA6D,aACA7B,kBACA+B,cAEA,eACAF,oBAKAG,+BAAA,WACAF,2DACA,6CAQA,GAPA,uBAEA,yBACA,2BAIA,kBACA,IACA,EADA,IACA,+DACA,8CACA,gDACA,yCACA,QAGA,+BACA,6CACA,gDACA,gDAIAD,aACA7B,kBACA+B,iBAGA,mBACA/D,6BACA6D,aACA7B,kBACA+B,kBAMAE,uBACA,IACA,wCACA,sBAEA,kFAEA,SACAjE,4BACA,sBAKAkE,2BACA,4BAEA,oBAEA,2EAGA,eACA,+DACA,8CACA,qBAKAC,wBACAnE,uBAIAoE,2BAAA,WAEA,6BACA,uCACA,2CACA,0BAGA,iCAGAC,uBACA,0BACA,IAGA,qDAIAC,yBAAA,WAEA,6BACA,qCACA,yCACA,0BAGA,+BAGAD,uBACA,0BACA,IAGA,mDAIAE,oCAEA,wBAGA,sCACA,qBAEA,8CAEA,oEACA,KAEA,YADA,4BAQAC,uBACA,yBAIAC,wBACA,+BACA,yBAIApD,4BACA,yCAIAqD,gCACA,uCACA,2BACA,uCACA,6CAEA,qCACA,2CAGA,2BAIAC,+BACA,2BAIAC,8BAAA,WACA,2BACAP,uBACA,yBACA,MAIAQ,0BAAA,WACA,wBACA,0BAEA,0CACA,8CACA,sBACA,wBAGA,mBACAR,uBACA,kBACA,MAIAS,0BACA,sBAIAC,4BACA,sBAIAC,6BACA,sBAEA,wBAAAC,SAAAC,UAAA7C,SACA,oBACA,aACA,gCAEA,yCACA,4BACA,iCAGA,2DAGA,uBAIA8C,wBACA,eACA,kCACA9C,6BACAA,mCAIA+C,wBAEA,+CASA,mDASA,OACAC,2BACAC,uBACAjD,uBACAkD,gCAIA,wCAGA,kCACAC,iBACAA,eAIA,MACAC,cAIAA,aAGA,cACAA,iBAIA5B,oCAGA,iFAGA,OACA6B,oDACAC,gDACAC,oCACAC,oCACAhD,gCAGA,mFAEAgB,cACAiC,gEAvDAjC,aACA7B,qBACA+B,mBAXAF,aACA7B,oBACA+B,eAmEAgC,uBACA,oBACA,iBACA,WACA,OAGA,wBACA,sBAGA,yBACA,2BAKAC,4BACA,IAEA,EAFA,gDAEA3C,GAAA,8CACA,kDACA,EADA,IACA4C,iBAAA,8CACA,qBAMA,YALA,EACA,6BAEA,6BAIA,+BAGA,qBAMA,YALA,EACA,6BAEA,6BAIA,gCAIAC,wBACA,oBAEArC,qCACAA,aACA7B,gBACA+B,oBAIA,c,4DCxlBA,wHAAqxC,eAAG,G", "file": "pages/index/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/index/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=57280228&scoped=true&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=57280228&scoped=true&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"57280228\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/index/index.vue\"\nexport default component.exports", "export * from \"-!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=template&id=57280228&scoped=true&\"", "var components\ntry {\n  components = {\n    uniCalendar: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-calendar/components/uni-calendar/uni-calendar\" */ \"@/uni_modules/uni-calendar/components/uni-calendar/uni-calendar.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var l0 = _vm.showStationPicker\n    ? _vm.__map(_vm.currentCity.sysAddressDto, function (place, index) {\n        var $orig = _vm.__get_orig(place)\n        var g0 =\n          _vm.currentCity.sysAddressDto &&\n          _vm.currentCity.sysAddressDto.length > 0\n        var m0 = g0 ? _vm.isPlaceSelected(place) : null\n        var m1 = g0 ? _vm.isPlaceSelected(place) : null\n        return {\n          $orig: $orig,\n          g0: g0,\n          m0: m0,\n          m1: m1,\n        }\n      })\n    : null\n  var g1 = _vm.showStationPicker\n    ? !_vm.currentCity.sysAddressDto ||\n      _vm.currentCity.sysAddressDto.length === 0\n    : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        l0: l0,\n        g1: g1,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"content\">\n\t\t<!-- 顶部背景和标语部分 -->\n\t\t<view class=\"header\">\n\t\t\t<image class=\"banner-image\" src=\"/static/images/banner.png\" mode=\"aspectFill\"></image>\n\t\t</view>\n\t\t\n\t\t<!-- 选择站点部分 -->\n\t\t<view class=\"station-selector\">\n\t\t\t<view class=\"station-item\" @click=\"selectDeparture\">\n\t\t\t\t<text class=\"station-name\">{{departureStation}}</text>\n\t\t\t</view>\n\t\t\t<view class=\"switch-btn\" @click=\"switchStations\">\n\t\t\t\t<image :class=\"['switch-icon', {rotating: isRotating}]\" src=\"/static/images/switch.png\" mode=\"aspectFit\"></image>\n\t\t\t</view>\n\t\t\t<view class=\"station-item\" @click=\"selectArrival\">\n\t\t\t\t<text class=\"station-name\">{{arrivalStation}}</text>\n\t\t\t</view>\n\t\t</view>\n\t\t\n\t\t<!-- 日期选择部分 -->\n\t\t<view class=\"date-selector\" @click=\"showDatePicker\">\n\t\t\t<text class=\"date\">{{date}}</text>\n\t\t\t<text class=\"week\">{{week}}</text>\n\t\t\t<text class=\"today\" v-if=\"isToday\">今天</text>\n\t\t</view>\n\t\t\n\t\t<!-- 查询按钮 -->\n\t\t<view class=\"query-btn\" @click=\"queryTickets\">查询车票</view>\n\t\t\n\t\t<!-- 历史记录 -->\n\t\t<view class=\"history\">\n\t\t\t<scroll-view class=\"history-scroll\" scroll-x=\"true\" show-scrollbar=\"false\">\n\t\t\t\t<view class=\"history-scroll-content\">\n\t\t\t\t\t<view class=\"history-item\" v-for=\"(item, index) in historyList\" :key=\"index\" @click=\"useHistory(item)\">\n\t\t\t\t\t\t<text class=\"history-text\">{{item}}</text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</scroll-view>\n\t\t\t<view class=\"history-header\">\n\t\t\t\t<text class=\"history-clear\" @click=\"clearHistory\">清除历史</text>\n\t\t\t</view>\n\t\t</view>\n\t\t\n\t\t<!-- uni-calendar日期选择器弹窗 -->\n\t\t<view class=\"calendar-popup\" v-if=\"showCalendar\">\n\t\t\t<view class=\"calendar-mask\" @click=\"cancelDatePicker\"></view>\n\t\t\t<view class=\"calendar-container\">\n\t\t\t\t<view class=\"calendar-header custom-calendar-header\">\n\t\t\t\t\t<text class=\"calendar-close\" @click=\"cancelDatePicker\">×</text>\n\t\t\t\t\t<text class=\"calendar-title\">请选择日期</text>\n\t\t\t\t</view>\n\t\t\t\t\n\t\t\t\t<!-- 使用uni-calendar组件 -->\n\t\t\t\t<uni-calendar\n\t\t\t\t\tclass=\"custom-calendar\"\n\t\t\t\t\tref=\"calendar\"\n\t\t\t\t\t:insert=\"true\"\n\t\t\t\t\t:lunar=\"false\"\n\t\t\t\t\t:start-date=\"startDate\"\n\t\t\t\t\t:end-date=\"endDate\"\n\t\t\t\t\t@change=\"calendarChange\"\n\t\t\t\t\t@monthSwitch=\"monthSwitch\"\n\t\t\t\t/>\n\t\t\t\t\n\t\t\t\t<view class=\"calendar-footer\">\n\t\t\t\t\t<view class=\"calendar-confirm\" @click=\"confirmDatePicker\">确认</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t\t\n\t\t<!-- 站点选择弹窗 -->\n\t\t<view class=\"station-popup\" v-if=\"showStationPicker\">\n\t\t\t<view class=\"station-mask\" @click=\"cancelStationPicker\"></view>\n\t\t\t<view :class=\"['station-container', {'station-container-show': stationAnimateShow}]\">\n\t\t\t\t<view class=\"station-header\">\n\t\t\t\t\t<text class=\"station-close\" @click=\"cancelStationPicker\">×</text>\n\t\t\t\t\t<text class=\"station-title\">{{isSelectingDeparture ? '出发地' : '到达地'}}</text>\n\t\t\t\t</view>\n\t\t\t\t\n\t\t\t\t<view class=\"station-tip\">\n\t\t\t\t\t<text class=\"tip-icon\">⚠</text>\n\t\t\t\t\t<text class=\"tip-text\">提示：如有疑问，可在微信群中联系客服咨询～</text>\n\t\t\t\t</view>\n\t\t\t\t\n\t\t\t\t<view class=\"station-content\">\n\t\t\t\t\t<!-- 左侧城市列表 -->\n\t\t\t\t\t<scroll-view class=\"city-list\" scroll-y=\"true\">\n\t\t\t\t\t\t<view \n\t\t\t\t\t\t\tv-for=\"(city, index) in cities\" \n\t\t\t\t\t\t\t:key=\"index\" \n\t\t\t\t\t\t\t:class=\"['city-item', { 'city-item-active': currentCityIndex === index }]\"\n\t\t\t\t\t\t\t@click=\"selectCity(index)\"\n\t\t\t\t\t\t>\n\t\t\t\t\t\t\t<text class=\"city-name\">{{city.addressName}}</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</scroll-view>\n\t\t\t\t\t\n\t\t\t\t\t<!-- 右侧站点列表 -->\n\t\t\t\t\t<scroll-view class=\"place-list\" scroll-y=\"true\">\n\t\t\t\t\t\t<view \n\t\t\t\t\t\t\tv-for=\"(place, index) in currentCity.sysAddressDto\" \n\t\t\t\t\t\t\t:key=\"index\" \n\t\t\t\t\t\t\t:class=\"['place-item', { 'place-item-selected': isPlaceSelected(place) }]\"\n\t\t\t\t\t\t\t@click=\"selectPlace(place)\"\n\t\t\t\t\t\t\tv-if=\"currentCity.sysAddressDto && currentCity.sysAddressDto.length > 0\"\n\t\t\t\t\t\t>\n\t\t\t\t\t\t\t<text class=\"place-name\">{{place.addressName}}</text>\n\t\t\t\t\t\t\t<text v-if=\"isPlaceSelected(place)\" class=\"place-check\">✓</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view v-if=\"!currentCity.sysAddressDto || currentCity.sysAddressDto.length === 0\" class=\"no-place\">\n\t\t\t\t\t\t\t<text class=\"no-place-text\">暂无站点</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</scroll-view>\n\t\t\t\t</view>\n\t\t\t\t\n\t\t\t\t<view class=\"station-footer\">\n\t\t\t\t\t<view class=\"station-confirm\" @click=\"confirmStationPicker\">确认</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t</view>\n</template>\n\n<script>\n\timport request from '../../utils/request.js';\n\t\n\texport default {\n\t\tdata() {\n\t\t\tconst now = new Date();\n\t\t\tconst year = now.getFullYear();\n\t\t\tconst month = now.getMonth() + 1;\n\t\t\tconst day = now.getDate();\n\t\t\tconst nowDateStr = `${year}-${month}-${day}`;\n\t\t\t\n\t\t\t// 设置最大日期为一年后\n\t\t\tconst endYear = month === 12 ? year + 1 : year;\n\t\t\tconst endMonth = month === 12 ? 1 : month + 1; \n\t\t\tconst endDateStr = `${endYear}-${endMonth}-${day}`;\n\t\t\t\n\t\t\treturn {\n\t\t\t\ttitle: '购票系统',\n\t\t\t\tdepartureStation: '', // 初始值设为空，将从接口获取\n\t\t\t\tarrivalStation: '', // 初始值设为空，将从接口获取\n\t\t\t\tdepartureStationId: null, // 出发地ID\n\t\t\t\tarrivalStationId: null, // 到达地ID\n\t\t\t\tdate: `${month}月${day}日`,\n\t\t\t\tweek: `星期${['日', '一', '二', '三', '四', '五', '六'][now.getDay()]}`,\n\t\t\t\tisToday: true,\n\t\t\t\thistoryList: [],\n\t\t\t\tisRotating: false,\n\t\t\t\tshowCalendar: false,\n\t\t\t\t\n\t\t\t\t// 日历相关数据\n\t\t\t\tstartDate: nowDateStr,\n\t\t\t\tendDate: `${year + 1}-${month}-${day}`,\n\t\t\t\tselectedDate: nowDateStr,\n\t\t\t\tselectedInfo: null,\n\t\t\t\t\n\t\t\t\t// 站点选择相关\n\t\t\t\tshowStationPicker: false,\n\t\t\t\tisSelectingDeparture: true,\n\t\t\t\ttempStation: '',\n\t\t\t\ttempStationId: null, // 临时选中的站点ID\n\t\t\t\tcurrentCityIndex: 0,\n\t\t\t\t// 替换静态城市数据为空数组，将通过API加载\n\t\t\t\tdepartureCities: [],\n\t\t\t\tarrivalCities: [],\n\t\t\t\tcities: [], // 当前显示的城市列表（根据选择出发地/目的地动态切换）\n\t\t\t\tisLoading: false, // 加载状态\n\t\t\t\tstationAnimateShow: false\n\t\t\t}\n\t\t},\n\t\tcomputed: {\n\t\t\tcurrentCity() {\n\t\t\t\treturn this.cities[this.currentCityIndex] || { addressName: '', sysAddressDto: [] };\n\t\t\t}\n\t\t},\n\t\tonLoad() {\n\t\t\t// 页面加载时获取历史记录和站点数据\n\t\t\tthis.loadHistory();\n\t\t\t\n\t\t\t// 加载出发地和目的地数据\n\t\t\tthis.loadDepartureStations();\n\t\t\tthis.loadArrivalStations();\n\t\t},\n\t\tmethods: {\n\t\t\t// 加载出发地数据\n\t\t\tloadDepartureStations() {\n\t\t\t\tthis.isLoading = true;\n\t\t\t\tuni.showLoading({\n\t\t\t\t\ttitle: '加载中...'\n\t\t\t\t});\n\t\t\t\t\n\t\t\t\trequest.get('/app/address/get?type=0').then(res => {\n\t\t\t\t\tif (res && res.code === 200 && res.data && res.data.length > 0) {\n\t\t\t\t\t\tthis.departureCities = res.data;\n\t\t\t\t\t\t// 如果当前正在选择出发地，则更新显示的城市列表\n\t\t\t\t\t\tif (this.isSelectingDeparture) {\n\t\t\t\t\t\t\tthis.cities = this.departureCities;\n\t\t\t\t\t\t}\n\t\t\t\t\t\t\n\t\t\t\t\t\t// 设置初始出发地（如果当前为空）\n\t\t\t\t\t\tif (!this.departureStation) {\n\t\t\t\t\t\t\t// 查找第一个有子站点的城市\n\t\t\t\t\t\t\tfor (let city of this.departureCities) {\n\t\t\t\t\t\t\t\tif (city.sysAddressDto && city.sysAddressDto.length > 0) {\n\t\t\t\t\t\t\t\t\tthis.departureStation = city.sysAddressDto[0].addressName;\n\t\t\t\t\t\t\t\t\tthis.departureStationId = city.sysAddressDto[0].id;\n\t\t\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t// 如果没有找到任何站点，使用第一个城市名称和ID\n\t\t\t\t\t\t\tif (!this.departureStation && this.departureCities.length > 0) {\n\t\t\t\t\t\t\t\tthis.departureStation = this.departureCities[0].addressName;\n\t\t\t\t\t\t\t\tthis.departureStationId = this.departureCities[0].id;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t} else {\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ttitle: '加载出发地数据失败',\n\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t});\n\t\t\t\t\t}\n\t\t\t\t\tthis.isLoading = false;\n\t\t\t\t\tuni.hideLoading();\n\t\t\t\t}).catch(err => {\n\t\t\t\t\tconsole.error('加载出发地数据失败', err);\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '加载出发地数据失败',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t});\n\t\t\t\t\tthis.isLoading = false;\n\t\t\t\t\tuni.hideLoading();\n\t\t\t\t});\n\t\t\t},\n\t\t\t\n\t\t\t// 加载目的地数据\n\t\t\tloadArrivalStations() {\n\t\t\t\trequest.get('/app/address/get?type=1').then(res => {\n\t\t\t\t\tif (res && res.code === 200 && res.data && res.data.length > 0) {\n\t\t\t\t\t\tthis.arrivalCities = res.data;\n\t\t\t\t\t\t// 如果当前正在选择目的地，则更新显示的城市列表\n\t\t\t\t\t\tif (!this.isSelectingDeparture) {\n\t\t\t\t\t\t\tthis.cities = this.arrivalCities;\n\t\t\t\t\t\t}\n\t\t\t\t\t\t\n\t\t\t\t\t\t// 设置初始到达地（如果当前为空）\n\t\t\t\t\t\tif (!this.arrivalStation) {\n\t\t\t\t\t\t\t// 查找第一个有子站点的城市\n\t\t\t\t\t\t\tfor (let city of this.arrivalCities) {\n\t\t\t\t\t\t\t\tif (city.sysAddressDto && city.sysAddressDto.length > 0) {\n\t\t\t\t\t\t\t\t\tthis.arrivalStation = city.sysAddressDto[0].addressName;\n\t\t\t\t\t\t\t\t\tthis.arrivalStationId = city.sysAddressDto[0].id;\n\t\t\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t// 如果没有找到任何站点，使用第一个城市名称和ID\n\t\t\t\t\t\t\tif (!this.arrivalStation && this.arrivalCities.length > 0) {\n\t\t\t\t\t\t\t\tthis.arrivalStation = this.arrivalCities[0].addressName;\n\t\t\t\t\t\t\t\tthis.arrivalStationId = this.arrivalCities[0].id;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t} else {\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ttitle: '加载目的地数据失败',\n\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t});\n\t\t\t\t\t}\n\t\t\t\t}).catch(err => {\n\t\t\t\t\tconsole.error('加载目的地数据失败', err);\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '加载目的地数据失败',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t});\n\t\t\t\t});\n\t\t\t},\n\t\t\t\n\t\t\t// 加载历史记录\n\t\t\tloadHistory() {\n\t\t\t\ttry {\n\t\t\t\t\tconst history = uni.getStorageSync('searchHistory');\n\t\t\t\t\tif (history && Array.isArray(history)) {\n\t\t\t\t\t\t// 将历史记录转换为显示格式\n\t\t\t\t\t\tthis.historyList = history.map(item => `${item.from}--${item.to}`);\n\t\t\t\t\t}\n\t\t\t\t} catch (e) {\n\t\t\t\t\tconsole.error('加载历史记录失败', e);\n\t\t\t\t\tthis.historyList = [];\n\t\t\t\t}\n\t\t\t},\n\t\t\t\n\t\t\t// 日历组件日期变化事件\n\t\t\tcalendarChange(e) {\n\t\t\t\tif (e.year && e.month && e.date) {\n\t\t\t\t\t// 保存选中的日期信息\n\t\t\t\t\tthis.selectedInfo = e;\n\t\t\t\t\t// 构建完整的日期字符串\n\t\t\t\t\tthis.selectedDate = `${e.year}-${e.month}-${e.date}`;\n\t\t\t\t\t\n\t\t\t\t\t// 判断是否为今天\n\t\t\t\t\tconst now = new Date();\n\t\t\t\t\tconst today = new Date(now.getFullYear(), now.getMonth(), now.getDate()).getTime();\n\t\t\t\t\tconst selected = new Date(e.year, e.month - 1, e.date).getTime();\n\t\t\t\t\tthis.isToday = (today === selected);\n\t\t\t\t}\n\t\t\t},\n\t\t\t\n\t\t\t// 日历组件月份切换事件\n\t\t\tmonthSwitch(e) {\n\t\t\t\tconsole.log('月份切换', e);\n\t\t\t},\n\t\t\t\n\t\t\t// 选择出发站\n\t\t\tselectDeparture() {\n\t\t\t\t// 打开站点选择页面\n\t\t\t\tthis.isSelectingDeparture = true;\n\t\t\t\tthis.tempStation = this.departureStation;\n\t\t\t\tthis.tempStationId = this.departureStationId;\n\t\t\t\tthis.showStationPicker = true;\n\n\t\t\t\t// 设置当前显示的城市列表为出发地列表\n\t\t\t\tthis.cities = this.departureCities;\n\n\t\t\t\t// 触发动画\n\t\t\t\tsetTimeout(() => {\n\t\t\t\t\tthis.stationAnimateShow = true;\n\t\t\t\t}, 50);\n\n\t\t\t\t// 根据当前站点设置选中的城市\n\t\t\t\tthis.setInitialCityByStation(this.departureStation);\n\t\t\t},\n\t\t\t\n\t\t\t// 选择到达站\n\t\t\tselectArrival() {\n\t\t\t\t// 打开站点选择页面\n\t\t\t\tthis.isSelectingDeparture = false;\n\t\t\t\tthis.tempStation = this.arrivalStation;\n\t\t\t\tthis.tempStationId = this.arrivalStationId;\n\t\t\t\tthis.showStationPicker = true;\n\n\t\t\t\t// 设置当前显示的城市列表为目的地列表\n\t\t\t\tthis.cities = this.arrivalCities;\n\n\t\t\t\t// 触发动画\n\t\t\t\tsetTimeout(() => {\n\t\t\t\t\tthis.stationAnimateShow = true;\n\t\t\t\t}, 50);\n\n\t\t\t\t// 根据当前站点设置选中的城市\n\t\t\t\tthis.setInitialCityByStation(this.arrivalStation);\n\t\t\t},\n\t\t\t\n\t\t\t// 根据站点名称找到对应的城市索引\n\t\t\tsetInitialCityByStation(stationName) {\n\t\t\t\t// 重置为默认选择第一个城市\n\t\t\t\tthis.currentCityIndex = 0;\n\t\t\t\t\n\t\t\t\t// 遍历当前城市列表，查找包含该站点的城市\n\t\t\t\tfor (let i = 0; i < this.cities.length; i++) {\n\t\t\t\t\tconst city = this.cities[i];\n\t\t\t\t\t// 检查城市是否有子站点\n\t\t\t\t\tif (city.sysAddressDto && city.sysAddressDto.length > 0) {\n\t\t\t\t\t\t// 查找匹配的站点\n\t\t\t\t\t\tconst found = city.sysAddressDto.some(place => place.addressName === stationName);\n\t\t\t\t\t\tif (found) {\n\t\t\t\t\t\t\tthis.currentCityIndex = i;\n\t\t\t\t\t\t\treturn;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t},\n\t\t\t\n\t\t\t// 选择城市\n\t\t\tselectCity(index) {\n\t\t\t\tthis.currentCityIndex = index;\n\t\t\t},\n\t\t\t\n\t\t\t// 选择站点\n\t\t\tselectPlace(place) {\n\t\t\t\tthis.tempStation = place.addressName;\n\t\t\t\tthis.tempStationId = place.id; // 记录选中站点的ID\n\t\t\t},\n\t\t\t\n\t\t\t// 判断站点是否被选中\n\t\t\tisPlaceSelected(place) {\n\t\t\t\treturn this.tempStation === place.addressName;\n\t\t\t},\n\t\t\t\n\t\t\t// 确认站点选择\n\t\t\tconfirmStationPicker() {\n\t\t\t\tif (this.tempStation && this.tempStationId) {\n\t\t\t\t\tif (this.isSelectingDeparture) {\n\t\t\t\t\t\tthis.departureStation = this.tempStation;\n\t\t\t\t\t\tthis.departureStationId = this.tempStationId;\n\t\t\t\t\t} else {\n\t\t\t\t\t\tthis.arrivalStation = this.tempStation;\n\t\t\t\t\t\tthis.arrivalStationId = this.tempStationId;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tthis.closeStationPicker();\n\t\t\t},\n\t\t\t\n\t\t\t// 取消站点选择\n\t\t\tcancelStationPicker() {\n\t\t\t\tthis.closeStationPicker();\n\t\t\t},\n\t\t\t\n\t\t\t// 关闭站点选择器（带动画）\n\t\t\tcloseStationPicker() {\n\t\t\t\tthis.stationAnimateShow = false;\n\t\t\t\tsetTimeout(() => {\n\t\t\t\t\tthis.showStationPicker = false;\n\t\t\t\t}, 300); // 等待动画完成\n\t\t\t},\n\t\t\t\n\t\t\t// 交换出发地和目的地\n\t\t\tswitchStations() {\n\t\t\t\tconst tempStation = this.departureStation;\n\t\t\t\tconst tempStationId = this.departureStationId;\n\n\t\t\t\tthis.departureStation = this.arrivalStation;\n\t\t\t\tthis.departureStationId = this.arrivalStationId;\n\t\t\t\tthis.arrivalStation = tempStation;\n\t\t\t\tthis.arrivalStationId = tempStationId;\n\n\t\t\t\t// 触发旋转动画\n\t\t\t\tthis.isRotating = true;\n\t\t\t\tsetTimeout(() => {\n\t\t\t\t\tthis.isRotating = false;\n\t\t\t\t}, 500); // 500ms后重置，与动画时间一致\n\t\t\t},\n\t\t\t\n\t\t\t// 显示日期选择器\n\t\t\tshowDatePicker() {\n\t\t\t\tthis.showCalendar = true;\n\t\t\t},\n\t\t\t\n\t\t\t// 取消日期选择\n\t\t\tcancelDatePicker() {\n\t\t\t\tthis.showCalendar = false;\n\t\t\t},\n\t\t\t\n\t\t\t// 确认日期选择\n\t\t\tconfirmDatePicker() {\n\t\t\t\tif (!this.selectedInfo) return;\n\n\t\t\t\tconst { year, month, date } = this.selectedInfo;\n\t\t\t\tconst selectedDate = new Date(year, month - 1, date);\n\t\t\t\tconst weekDay = selectedDate.getDay();\n\t\t\t\tconst weekDayNames = ['日', '一', '二', '三', '四', '五', '六'];\n\n\t\t\t\tthis.date = `${month}月${date}日`;\n\t\t\t\tthis.week = `星期${weekDayNames[weekDay]}`;\n\t\t\t\tthis.isToday = this.isDateToday(selectedDate);\n\n\t\t\t\t// 更新selectedDate变量，用于传递给车票列表页面\n\t\t\t\tthis.selectedDate = `${year}-${month}-${date}`;\n\n\t\t\t\t// 关闭日历弹窗\n\t\t\t\tthis.showCalendar = false;\n\t\t\t},\n\t\t\t\n\t\t\t// 检查日期是否是今天\n\t\t\tisDateToday(date) {\n\t\t\t\tconst today = new Date();\n\t\t\t\treturn date.getDate() === today.getDate() && \n\t\t\t\t\t   date.getMonth() === today.getMonth() && \n\t\t\t\t\t   date.getFullYear() === today.getFullYear();\n\t\t\t},\n\t\t\t\n\t\t\t// 查询车票\n\t\t\tqueryTickets() {\n\t\t\t\t// 验证出发地和目的地不能相同\n\t\t\t\tif(this.departureStation === this.arrivalStation) {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '出发地和目的地不能相同',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t});\n\t\t\t\t\treturn;\n\t\t\t\t}\n\n\t\t\t\t// 验证是否有地点ID\n\t\t\t\tif(!this.departureStationId || !this.arrivalStationId) {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '请重新选择出发地和目的地',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t});\n\t\t\t\t\treturn;\n\t\t\t\t}\n\n\t\t\t\t// 保存搜索历史\n\t\t\t\tconst searchItem = {\n\t\t\t\t\tfrom: this.departureStation,\n\t\t\t\t\tto: this.arrivalStation,\n\t\t\t\t\tdate: this.selectedDate,\n\t\t\t\t\ttimestamp: new Date().getTime()\n\t\t\t\t};\n\n\t\t\t\t// 获取现有历史记录\n\t\t\t\tlet searchHistory = uni.getStorageSync('searchHistory') || [];\n\n\t\t\t\t// 检查是否已存在相同的搜索记录（只基于出发地和目的地，不考虑日期）\n\t\t\t\tconst existIndex = searchHistory.findIndex(item =>\n\t\t\t\t\titem.from === searchItem.from &&\n\t\t\t\t\titem.to === searchItem.to\n\t\t\t\t);\n\n\t\t\t\t// 如果存在，先从历史中删除\n\t\t\t\tif(existIndex > -1) {\n\t\t\t\t\tsearchHistory.splice(existIndex, 1);\n\t\t\t\t}\n\n\t\t\t\t// 将新的搜索添加到历史开头\n\t\t\t\tsearchHistory.unshift(searchItem);\n\n\t\t\t\t// 限制历史记录数量为10条\n\t\t\t\tif(searchHistory.length > 10) {\n\t\t\t\t\tsearchHistory = searchHistory.slice(0, 10);\n\t\t\t\t}\n\n\t\t\t\t// 保存回本地存储\n\t\t\t\tuni.setStorageSync('searchHistory', searchHistory);\n\n\t\t\t\t// 更新页面显示的历史记录\n\t\t\t\tthis.historyList = searchHistory.map(item => `${item.from}--${item.to}`);\n\n\t\t\t\t// 导航到车票查询结果页面，传递地点名称、ID和日期\n\t\t\t\tconst params = {\n\t\t\t\t\tdeparture: encodeURIComponent(this.departureStation),\n\t\t\t\t\tarrival: encodeURIComponent(this.arrivalStation),\n\t\t\t\t\tupAddressId: this.departureStationId,\n\t\t\t\t\tdownAddressId: this.arrivalStationId,\n\t\t\t\t\tselectedDate: this.selectedDate // 传递选择的日期\n\t\t\t\t};\n\n\t\t\t\tconst queryString = Object.keys(params).map(key => `${key}=${params[key]}`).join('&');\n\n\t\t\t\tuni.navigateTo({\n\t\t\t\t\turl: `/subpkg-booking/ticket_list/ticket_list?${queryString}`\n\t\t\t\t});\n\t\t\t},\n\t\t\t\n\t\t\t// 使用历史记录\n\t\t\tuseHistory(item) {\n\t\t\t\tconst stations = item.split('--');\n\t\t\t\tif (stations.length === 2) {\n\t\t\t\t\tconst fromStation = stations[0];\n\t\t\t\t\tconst toStation = stations[1];\n\n\t\t\t\t\t// 设置出发地和目的地\n\t\t\t\t\tthis.departureStation = fromStation;\n\t\t\t\t\tthis.arrivalStation = toStation;\n\n\t\t\t\t\t// 查找对应的站点ID\n\t\t\t\t\tthis.findStationId(fromStation, true); // 查找出发地ID\n\t\t\t\t\tthis.findStationId(toStation, false); // 查找目的地ID\n\t\t\t\t}\n\t\t\t},\n\n\t\t\t// 根据站点名称查找站点ID\n\t\t\tfindStationId(stationName, isDeparture) {\n\t\t\t\tconst cities = isDeparture ? this.departureCities : this.arrivalCities;\n\n\t\t\t\tfor (let city of cities) {\n\t\t\t\t\tif (city.sysAddressDto && city.sysAddressDto.length > 0) {\n\t\t\t\t\t\tfor (let station of city.sysAddressDto) {\n\t\t\t\t\t\t\tif (station.addressName === stationName) {\n\t\t\t\t\t\t\t\tif (isDeparture) {\n\t\t\t\t\t\t\t\t\tthis.departureStationId = station.id;\n\t\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\t\tthis.arrivalStationId = station.id;\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\treturn;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t\t// 如果没有子站点，检查城市名称\n\t\t\t\t\tif (city.addressName === stationName) {\n\t\t\t\t\t\tif (isDeparture) {\n\t\t\t\t\t\t\tthis.departureStationId = city.id;\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tthis.arrivalStationId = city.id;\n\t\t\t\t\t\t}\n\t\t\t\t\t\treturn;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t},\n\t\t\t\n\t\t\t// 清除历史记录\n\t\t\tclearHistory() {\n\t\t\t\tthis.historyList = [];\n\t\t\t\t// 清除本地存储\n\t\t\t\tuni.removeStorageSync('searchHistory');\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '历史记录已清除',\n\t\t\t\t\ticon: 'success'\n\t\t\t\t});\n\t\t\t}\n\t\t}\n\t}\n</script>\n\n<style scoped lang=\"scss\">\n\tpage {\n\t\toverflow: hidden;\n\t\theight: 100%;\n\t}\n\t\n\t.content {\n\t\twidth: 100%;\n\t\t// height: 100vh;\n\t\tbackground-color: #ffffff;\n\t\toverflow: hidden;\n\t\tposition: relative;\n\t}\n\t\n\t/* 顶部背景和标语 */\n\t.header {\n\t\twidth: 100%;\n\t\theight: 400rpx;\n\t\tbackground: linear-gradient(to bottom, #a3c0f8, #d9e5fc);\n\t\tbox-sizing: border-box;\n\t}\n\t\n\t.banner-image {\n\t\twidth: 100%;\n\t\theight: 100%;\n\t\tobject-fit: cover;\n\t}\n\t\n\t/* 选择站点 */\n\t.station-selector {\n\t\twidth: 90%;\n\t\tmargin: 20rpx auto;\n\t\tdisplay: flex;\n\t\tjustify-content: space-between;\n\t\talign-items: center;\n\t}\n\t\n\t.station-item {\n\t\tflex: 1;\n\t\tdisplay: flex;\n\t\tjustify-content: center;\n\t}\n\n\t.station-name {\n\t\tfont-size: 36rpx;\n\t\tfont-weight: bold;\n\t\tcolor: #333;\n\t}\n\t\n\t.switch-btn {\n\t\twidth: 80rpx;\n\t\theight: 80rpx;\n\t\tdisplay: flex;\n\t\tjustify-content: center;\n\t\talign-items: center;\n\t}\n\t\n\t.switch-icon {\n\t\twidth: 60rpx;\n\t\theight: 60rpx;\n\t\ttransition: all 0.5s ease;\n\t}\n\t\n\t/* 添加旋转动画 */\n\t.rotating {\n\t\tanimation: rotate360 0.5s linear;\n\t}\n\t\n\t@keyframes rotate360 {\n\t\tfrom {\n\t\t\ttransform: rotate(0deg);\n\t\t}\n\t\tto {\n\t\t\ttransform: rotate(360deg);\n\t\t}\n\t}\n\t\n\t/* 日期选择 */\n\t.date-selector {\n\t\twidth: 90%;\n\t\tmargin: 20rpx auto;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\theight: 80rpx;\n\t\tborder-bottom: 1px solid #eee;\n\t}\n\t\n\t.date {\n\t\tfont-size: 32rpx;\n\t\tcolor: #333;\n\t\tmargin-right: 20rpx;\n\t}\n\t\n\t.week {\n\t\tfont-size: 32rpx;\n\t\tcolor: #333;\n\t\tmargin-right: 20rpx;\n\t}\n\t\n\t.today {\n\t\tfont-size: 28rpx;\n\t\tcolor: #999;\n\t\tbackground-color: #f5f5f5;\n\t\tpadding: 4rpx 16rpx;\n\t\tborder-radius: 20rpx;\n\t}\n\t\n\t/* 查询按钮 */\n\t.query-btn {\n\t\twidth: 90%;\n\t\theight: 90rpx;\n\t\tbackground: linear-gradient(to right, #3a97fa, #3b87f7);\n\t\tcolor: #fff;\n\t\tborder-radius: 45rpx;\n\t\tmargin: 30rpx auto;\n\t\tdisplay: flex;\n\t\tjustify-content: center;\n\t\talign-items: center;\n\t\tfont-size: 36rpx;\n\t}\n\t\n\t/* 历史记录 */\n\t.history {\n\t\twidth: 90%;\n\t\tmargin: 20rpx auto;\n\t\tdisplay: flex;\n\t}\n\t\n\t.history-header {\n\t\tdisplay: flex;\n\t\tjustify-content: flex-end;\n\t\talign-items: center;\n\t\tpadding: 10rpx 10rpx;\n\t}\n\t\n\t.history-clear {\n\t\tfont-size: 28rpx;\n\t\tcolor: #999;\n\t}\n\t\n\t.history-scroll {\n\t\twidth: 80%;\n\t\twhite-space: nowrap;\n\t}\n\t\n\t.history-scroll-content {\n\t\tdisplay: inline-block;\n\t\twhite-space: nowrap;\n\t}\n\t\n\t.history-item {\n\t\tdisplay: inline-block;\n\t\tmargin-right: 20rpx;\n\t\tpadding: 8rpx 20rpx;\n\t\tbackground-color: #f5f5f5;\n\t\tborder-radius: 30rpx;\n\t}\n\t\n\t.history-text {\n\t\tfont-size: 28rpx;\n\t\tcolor: #666;\n\t}\n\t\n\t/* 日历弹窗 */\n\t.calendar-popup {\n\t\tposition: fixed;\n\t\ttop: 0;\n\t\tleft: 0;\n\t\tright: 0;\n\t\tbottom: 0;\n\t\tz-index: 999;\n\t}\n\t\n\t.calendar-mask {\n\t\tposition: absolute;\n\t\ttop: 0;\n\t\tleft: 0;\n\t\tright: 0;\n\t\tbottom: 0;\n\t\tbackground-color: rgba(0, 0, 0, 0.5);\n\t}\n\t\n\t.calendar-container {\n\t\tposition: absolute;\n\t\tleft: 0;\n\t\tright: 0;\n\t\tbottom: 0;\n\t\tbackground-color: #fff;\n\t\tborder-radius: 20rpx 20rpx 0 0;\n\t\tpadding: 30rpx;\n\t}\n\t\n\t.custom-calendar-header {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tposition: relative;\n\t\tmargin-bottom: 20rpx;\n\t}\n\t\n\t.calendar-close {\n\t\tposition: absolute;\n\t\tleft: 0;\n\t\tfont-size: 48rpx;\n\t\tcolor: #333;\n\t\tline-height: 1;\n\t}\n\t\n\t.calendar-title {\n\t\tfont-size: 34rpx;\n\t\tfont-weight: bold;\n\t}\n\t\n\t.calendar-footer {\n\t\tmargin-top: 20rpx;\n\t\tpadding-bottom: 20rpx;\n\t}\n\t\n\t.calendar-confirm {\n\t\theight: 90rpx;\n\t\tbackground-color: #333;\n\t\tcolor: #fff;\n\t\tborder-radius: 45rpx;\n\t\tdisplay: flex;\n\t\tjustify-content: center;\n\t\talign-items: center;\n\t\tfont-size: 32rpx;\n\t}\n\t\n\t/* 站点选择器弹窗 */\n\t.station-popup {\n\t\tposition: fixed;\n\t\ttop: 0;\n\t\tleft: 0;\n\t\tright: 0;\n\t\tbottom: 0;\n\t\tz-index: 999;\n\t}\n\t\n\t.station-mask {\n\t\tposition: absolute;\n\t\ttop: 0;\n\t\tleft: 0;\n\t\tright: 0;\n\t\tbottom: 0;\n\t\tbackground-color: rgba(0, 0, 0, 0.5);\n\t}\n\t\n\t.station-container {\n\t\tposition: absolute;\n\t\ttop: 120rpx;\n\t\tleft: 0;\n\t\tright: 0;\n\t\tbottom: 0;\n\t\tbackground-color: #fff;\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\tborder-radius: 20rpx 20rpx 0 0; /* 添加顶部圆角 */\n\t\ttransform: translateY(100%); /* 初始位置在屏幕下方 */\n\t\ttransition: transform 0.3s ease; /* 添加过渡效果 */\n\t}\n\t\n\t.station-container-show {\n\t\ttransform: translateY(0); /* 显示状态 */\n\t}\n\t\n\t.station-header {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tposition: relative;\n\t\tpadding: 30rpx 0;\n\t\tborder-bottom: 1px solid #f0f0f0;\n\t}\n\t\n\t.station-close {\n\t\tposition: absolute;\n\t\tleft: 30rpx;\n\t\tfont-size: 48rpx;\n\t\tcolor: #333;\n\t\tline-height: 1;\n\t}\n\t\n\t.station-title {\n\t\tfont-size: 36rpx;\n\t\tfont-weight: bold;\n\t}\n\t\n\t.station-tip {\n\t\tbackground-color: #f0f8ff;\n\t\tpadding: 20rpx 30rpx;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t}\n\t\n\t.tip-icon {\n\t\tcolor: #FFB700;\n\t\tfont-size: 32rpx;\n\t\tmargin-right: 10rpx;\n\t}\n\t\n\t.tip-text {\n\t\tcolor: #666;\n\t\tfont-size: 28rpx;\n\t}\n\t\n\t.station-content {\n\t\tdisplay: flex;\n\t\tflex: 1;\n\t\toverflow: hidden;\n\t}\n\t\n\t.city-list {\n\t\twidth: 200rpx;\n\t\theight: 100%;\n\t\tbackground-color: #f8f8f8;\n\t}\n\t\n\t.city-item {\n\t\tpadding: 30rpx 20rpx;\n\t\ttext-align: center;\n\t}\n\t\n\t.city-item-active {\n\t\tbackground-color: #fff;\n\t\tposition: relative;\n\t}\n\t\n\t.city-item-active::before {\n\t\tcontent: '';\n\t\tposition: absolute;\n\t\tleft: 0;\n\t\ttop: 30%;\n\t\theight: 40%;\n\t\twidth: 8rpx;\n\t\tbackground-color: #3F8DF9;\n\t}\n\t\n\t.city-name {\n\t\tfont-size: 28rpx;\n\t\tcolor: #333;\n\t}\n\t\n\t.place-list {\n\t\tflex: 1;\n\t\theight: 100%;\n\t\tpadding: 0 30rpx;\n\t}\n\t\n\t.place-item {\n\t\tdisplay: flex;\n\t\tjustify-content: space-between;\n\t\talign-items: center;\n\t\tpadding: 30rpx 0;\n\t\tborder-bottom: 1px solid #f0f0f0;\n\t}\n\t\n\t.place-name {\n\t\tfont-size: 32rpx;\n\t\tcolor: #333;\n\t}\n\t\n\t.place-item-selected .place-name {\n\t\tcolor: #3F8DF9;\n\t}\n\t\n\t.place-check {\n\t\tcolor: #3F8DF9;\n\t\tfont-size: 32rpx;\n\t\tfont-weight: bold;\n\t}\n\t\n\t.station-footer {\n\t\tpadding: 20rpx 30rpx 40rpx;\n\t}\n\t\n\t.station-confirm {\n\t\theight: 90rpx;\n\t\tbackground-color: #333;\n\t\tcolor: #fff;\n\t\tborder-radius: 45rpx;\n\t\tdisplay: flex;\n\t\tjustify-content: center;\n\t\talign-items: center;\n\t\tfont-size: 32rpx;\n\t}\n\t\n\t/* 自定义uni-calendar样式 */\n\t.custom-calendar {\n\t\t--calendar-border-color: #f5f5f5;\n\t\t--calendar-text-color: #333;\n\t\t--calendar-lunar-color: #999;\n\t\t--calendar-background-color: #fff;\n\t\t--calendar-selected-background-color: #3F8DF9;\n\t\t--calendar-selected-lunar-color: #fff;\n\t\t--calendar-selected-text-color: #fff;\n\t}\n\t\n\t/* uni-calendar组件样式修改 */\n\t:deep(.uni-calendar) {\n\t\tbackground-color: #ffffff;\n\t}\n\t\n\t:deep(.uni-calendar__header) {\n\t\tdisplay: none !important;\n\t}\n\t\n\t:deep(.uni-calendar__weeks) {\n\t\tpadding: 10rpx 0;\n\t}\n\t\n\t:deep(.uni-calendar__weeks-day) {\n\t\theight: 90rpx;\n\t}\n\t\n\t:deep(.uni-calendar__weeks-day-text) {\n\t\tfont-size: 30rpx;\n\t\tcolor: #333;\n\t}\n\t\n\t:deep(.uni-calendar__selected) {\n\t\tbackground-color: #3F8DF9;\n\t\tcolor: #fff;\n\t\tborder-radius: 50%;\n\t\twidth: 70rpx;\n\t\theight: 70rpx;\n\t\tline-height: 70rpx;\n\t\ttext-align: center;\n\t}\n\t\n\t:deep(.uni-calendar__disabled) {\n\t\tcolor: #ccc;\n\t\tcursor: default;\n\t}\n\t\n\t:deep(.uni-calendar-item--disable) {\n\t\tcolor: #ccc;\n\t\tcursor: default;\n\t}\n\t\n\t:deep(.uni-calendar-item--before-checked), \n\t:deep(.uni-calendar-item--after-checked) {\n\t\tbackground-color: rgba(63, 141, 249, 0.1);\n\t\tcolor: #333;\n\t}\n\t\n\t:deep(.uni-calendar__week-day) {\n\t\theight: 80rpx;\n\t\tline-height: 80rpx;\n\t\ttext-align: center;\n\t\tfont-size: 28rpx;\n\t\tcolor: #333;\n\t}\n\t\n\t:deep(.uni-calendar__week-day-text) {\n\t\tcolor: #333;\n\t}\n\t\n\t:deep(.uni-calendar__weeks-day-text) {\n\t\tcolor: #333;\n\t}\n\t\n\t:deep(.uni-calendar-item__weeks-box-circle) {\n\t\tborder: 2rpx solid #3F8DF9;\n\t\tcolor: #3F8DF9;\n\t\tborder-radius: 50%;\n\t}\n\t\n\t.no-place {\n\t\tpadding: 50rpx 0;\n\t\ttext-align: center;\n\t}\n\t\n\t.no-place-text {\n\t\tfont-size: 28rpx;\n\t\tcolor: #999;\n\t}\n</style>\n", "import mod from \"-!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=style&index=0&id=57280228&scoped=true&lang=scss&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=style&index=0&id=57280228&scoped=true&lang=scss&\""], "sourceRoot": ""}