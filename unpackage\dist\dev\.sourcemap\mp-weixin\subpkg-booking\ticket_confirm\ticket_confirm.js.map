{"version": 3, "sources": ["uni-app:///main.js", "webpack:///E:/购票系统/购票系统/subpkg-booking/ticket_confirm/ticket_confirm.vue?6b93", "webpack:///E:/购票系统/购票系统/subpkg-booking/ticket_confirm/ticket_confirm.vue?7227", "webpack:///E:/购票系统/购票系统/subpkg-booking/ticket_confirm/ticket_confirm.vue?ab54", "webpack:///E:/购票系统/购票系统/subpkg-booking/ticket_confirm/ticket_confirm.vue?9236", "uni-app:///subpkg-booking/ticket_confirm/ticket_confirm.vue", "webpack:///E:/购票系统/购票系统/subpkg-booking/ticket_confirm/ticket_confirm.vue?66f9"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "renderjs", "component", "options", "__file", "components", "render", "_vm", "this", "_h", "$createElement", "g0", "_self", "_c", "totalPrice", "toFixed", "g1", "discount", "g2", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "couponDiscount", "g3", "finalPrice", "g4", "$mp", "data", "Object", "assign", "$root", "recyclableRender", "staticRenderFns", "_withStripped", "i", "ticketId", "loading", "loadingText", "departure", "departureDoor", "arrival", "date", "dateDesc", "time", "duration", "typeName", "type", "departureLocation", "originalPrice", "currentPrice", "passengerList", "contactPhone", "remark", "termsAgreed", "showCouponPopup", "couponList", "onLoad", "uni", "title", "methods", "loadUserContactInfo", "console", "loadCouponList", "request", "response", "processCouponsData", "id", "amount", "expireDate", "selected", "condition", "couponId", "formatDate", "calculatePriceFromApi", "selectedCount", "requestData", "number", "loadTicketDetail", "ticketApi", "icon", "processTicketDetail", "extractTimeFromDateTime", "timeStr", "getTicketType", "loadBackupData", "loadBackupDataFromUrl", "showAddressDetail", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "goToPassengerManagement", "url", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "success", "res", "index", "passenger", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "updatePassenger", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "receiveSelectedPassengers", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "p", "selectCoupon", "closeCouponPopup", "chooseCoupon", "currentCoupon", "item", "confirmCoupon", "toggleTerms", "viewTerms", "config<PERSON>pi", "content", "showCancel", "confirmText", "confirmPayment", "phoneRegex", "mask", "orderData", "phone", "sysPassengerInformations", "name", "idNumber", "callWechatPayWithParams", "provider", "appid", "timeStamp", "nonceStr", "package", "signType", "paySign", "paymentParams", "setTimeout", "fail", "errorMsg", "callWechatPay", "appId", "generateNonceStr", "result", "calculatePrice"], "mappings": "sKAAA,MAGA,aACA,WAFAA,EAAGC,kCAAoCC,EAGvCC,EAAWC,a,+ECLX,iIACIC,EADJ,QASIC,EAAY,qBACd,aACA,YACA,sBACA,EACA,KACA,WACA,MACA,EACA,gBACAD,GAGFC,EAAUC,QAAQC,OAAS,mDACZ,aAAAF,E,yCCvBf,sQ,gCCAA,IAAIG,EAAJ,0LACA,IAAIC,EAAS,WACX,IAAIC,EAAMC,KACNC,EAAKF,EAAIG,eAETC,GADKJ,EAAIK,MAAMC,GACVN,EAAIO,WAAWC,QAAQ,IAC5BC,EAAKT,EAAIU,SAASF,QAAQ,GAC1BG,EAAOX,EAAIY,eAAiBZ,EAAIa,eAAeL,QAAQ,GAAK,KAC5DM,EAAKd,EAAIe,WAAWP,QAAQ,GAC5BQ,EAAKhB,EAAIe,WAAWP,QAAQ,GAChCR,EAAIiB,IAAIC,KAAOC,OAAOC,OACpB,GACA,CACEC,MAAO,CACLjB,GAAIA,EACJK,GAAIA,EACJE,GAAIA,EACJG,GAAIA,EACJE,GAAIA,MAKRM,GAAmB,EACnBC,EAAkB,GACtBxB,EAAOyB,eAAgB,G,gCCzBvB,wHAA+qB,eAAG,G,+JCwLlrB,QACA,WACA,+PAAAC,EAAA,EAAAA,EAAA,iBAAAA,IAAA,uBAAAA,GAAA,UAAAA,GAAA,GAAAA,EAAA,sRAEA,CACAP,gBACA,OAEAQ,cAGAC,WACAC,qBAGAC,aACAC,iBACAC,WACAC,QACAC,YACAC,QACAC,YACAC,YACAC,QACAC,qBAGAC,gBACAC,eACA9B,WACAK,aAGA0B,iBACAC,gBACAC,UAGApC,eAGAqC,eAGAC,mBACAC,cACAlC,oBACAC,mBAIAkC,mBAEA,2BAGA,sBAGA,eACA,yBAGA,mDACA,+CACA,yCACA,iDAGA,8BACAC,yBACAC,6DAKA,yBAGA,wBAIAC,SAEAC,+BACA,IACA,8BACA,YAEA,0BACAC,gDAEAA,uCACA,sBAEA,SACAA,+BACA,uBAKAC,0BAAA,0IAEA,OAFA,SAEAD,4BAAA,SACAE,uCAAA,OAAAC,SAEA,yBAEA,0CACAH,yCAEAA,6BACA,iBACA,mDAEAA,iCACA,wEAfA,IAoBAI,+BAAA,WACA,6BAEA,qBACA,iBAEA,8BAEA,OACAC,QACAC,qBACAT,0BACAZ,WACAsB,aACAC,YACAC,aACAC,yBAMAC,uBACA,eAEA,IACA,kBACA,kBACA,yCACA,sCACA,uCACA,yCACA,2EACA,SAEA,OADAX,4BACA,IAKAY,iCAAA,gJAGA,GAHA,SAGAC,sCAAA,4BAEAA,OAAA,eAKA,OAJAb,8BAEA,eACA,aACA,yCAWA,OAPAA,+CACAc,GACAxC,oBACAyC,SACAL,0DAGAV,8BAAA,UAEAE,+CAAA,QAAAC,SAEA,yBACAH,2BAGAlC,SACA,gCACA,8BACA,yBAGA,sDACA,qBACA,oBAGAkC,uBACA7C,wBACAG,oBACAG,gCACAE,2BAGAqC,8BACA,qDAEAA,kCAAA,yDAlDA,IAuDAgB,4BAAA,0IAOA,OAPA,SAEA,aACA,4BAEAhB,qCAEA,SACAiB,sCAAA,UAAAd,UAEAA,YAAA,gBAEA,4DAEA,mFAIAH,gCACAJ,aACAC,iBACAqB,cAIA,kCAEA,OAFA,UAEA,yFA3BA,IAgCAC,gCAEA,wDACA,wBACA,4CACA,mCACA,2BACA,kDACA,yDACA,wCACA,2CAGA,sBAEAnB,yBACAlB,eACAE,uBACAG,iCACAC,kCAKAgC,oCACA,MACA,cAGA,IACA,SAEA,mBAEAC,sBACA,qBAKA,SAHAA,kBAOA,sBACA,iBAGA,QACA,SAEA,OADArB,+BACA,UAKAsB,0BACA,UACA,OACA,UACA,iBACA,OACA,UACA,QACA,iBAKAC,2BAEA,OACA9C,iBACAC,mBACAC,gBACAC,cACAC,cACAC,aACAC,gBACAC,eACAC,gBACAE,kBACAC,gBACA9B,YAIA,4BACA,4DACA,wEACA,sDACA,6CACA,yDACA,6CACA,yDACA,yDACA,yBACA,gEACA,6DACA,kDAGAS,sBAIA6B,yBACAC,6DAIA,uBAIA2B,iCAEA,wBACA,gBACA,YAEA,IACA,6DACA,8CACA,qDACA,oDACA,2BACA,oDACA,iDACA,wCAGA,wBAKAC,+BAKAC,4BAEA,6BACA,+DACA,sBAEA,8BAGA,gCAKAC,mCACA/B,cACAgC,gEAKAC,0BAEA,4BAGAjC,cACAgC,iDACAE,oBAEAC,2CACAC,QACAC,kBAOAC,wBAEAtC,cACAgC,oDAKAO,4BACA,2BAEA,sBAEA,8BAIAC,8BACA,oCACA,wBAEA,sBAEA,+BAKAC,4BACA,oCACA,+BAEA,sBAEA,+BAKAC,sCAAA,WAEAC,uBAEA,+CACAC,oCAGA,GAEA,4BACAP,OACAzB,YACAvB,iBAMA,sBAEA,6BAGAW,aACAC,mCACAqB,kBAKAuB,wBAEA,wBAGA,kFAIAC,4BACA,yBAIAC,yBACA,yBAGA,YACAC,cACA,2BAGA,qCACAC,iBAIAD,cACA,uBAGA5C,4CAIA8C,yBAEA,sBACA,uBAIA,6BAGA,yBAIAC,uBACA,oCAIAC,qBAAA,+HAOA,OAPA,SAGApD,eACAC,iBAGA,SACAoD,oCAAA,OAAA9C,SAEAP,gBAEA,gBAEAA,aACAC,aACAqD,sBACAC,cACAC,qBAGAxD,aACAC,iBACAqB,cAEA,mDAEAtB,gBACAI,gCACAJ,aACAC,iBACAqB,cACA,wDAhCA,IAqCAmC,0BAAA,mJAEA,8BAIA,OAHAzD,aACAC,qBACAqB,cACA,0BAKA,GAAAqB,sCAAA,qBACAA,cAAA,eAIA,OAHA3C,aACAC,eACAqB,cACA,6BAKA,4DAIA,OAHAtB,aACAC,gBACAqB,cACA,2BAKA,GAAAoC,kBACAA,wBAAA,gBAIA,OAHA1D,aACAC,mBACAqB,cACA,2BAwBA,OAxBA,UAKAtB,eACAC,kBACA0D,UAIAC,GACAlF,oBACAoC,mDACA+C,+BACAlE,oBACAmE,4CAAA,OACArD,QACAsD,YACAC,sCAKA5D,2BAAA,UAEAE,+BAAA,WAAAC,UAEAA,iBAAA,gBACAH,yBAGA,sBACAJ,eACAC,kBACA0D,UAIA,oCAEA3D,gBACAA,aACAC,eACAqB,kBAEA,8BAEA,yFAGAtB,gBACAI,8BACAJ,aACAC,iCACAqB,cACA,0DA3FA,IAgGA2C,oCACAjE,gBAEAI,4BAGA,OACA8D,iBACAC,cACAC,sBACAC,oBACAC,mBACAC,oBACAC,mBAGApE,yBAGAJ,wBACAyE,OACAvC,oBACA9B,uBACAJ,aACAC,aACAqB,iBAIAoD,uBACA1E,cACAgC,mDAEA,OAEA2C,iBACAvE,yBACA,iBAEA,WACA,4BACAwE,UACA,4BACAA,oBAIA5E,aACAC,QACAqB,mBAOAuD,0BAEA,4CAGA,0BAGA,uBAEA7E,gBAEAI,0BACA0E,QACAV,YACAC,WACAC,UACAC,iBAIAvE,kBACAkE,iBACA,SACAE,aACAC,YACAC,WACAC,YACAC,WACAtC,oBACA9B,uBACAJ,aACAC,aACAqB,iBAIAoD,uBACA1E,YACAgC,mDAEA,OAEA2C,iBACAvE,yBACA,iBAEA,WACA,4BACAwE,UACA,4BACAA,oBAIA5E,aACAC,QACAqB,kBAOAyD,4BAGA,IAFA,uEACA,KACA,aACAC,gDAEA,UAIAC,0BAEA,yEAGA,SAKA,OAJA,kBACA,gBACA,2BACA,mBAKA,qCACA,kBAGA,wDACA,2BAGA,c,4DCjgCA,wHAA8xC,eAAG,G", "file": "subpkg-booking/ticket_confirm/ticket_confirm.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './subpkg-booking/ticket_confirm/ticket_confirm.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./ticket_confirm.vue?vue&type=template&id=466fd826&scoped=true&\"\nvar renderjs\nimport script from \"./ticket_confirm.vue?vue&type=script&lang=js&\"\nexport * from \"./ticket_confirm.vue?vue&type=script&lang=js&\"\nimport style0 from \"./ticket_confirm.vue?vue&type=style&index=0&id=466fd826&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"466fd826\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"subpkg-booking/ticket_confirm/ticket_confirm.vue\"\nexport default component.exports", "export * from \"-!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./ticket_confirm.vue?vue&type=template&id=466fd826&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.totalPrice.toFixed(2)\n  var g1 = _vm.discount.toFixed(2)\n  var g2 = !!_vm.selectedCoupon ? _vm.couponDiscount.toFixed(2) : null\n  var g3 = _vm.finalPrice.toFixed(2)\n  var g4 = _vm.finalPrice.toFixed(2)\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        g1: g1,\n        g2: g2,\n        g3: g3,\n        g4: g4,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./ticket_confirm.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./ticket_confirm.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"content\">\r\n\t\t<!-- 加载状态 -->\r\n\t\t<view v-if=\"loading\" class=\"loading-container\">\r\n\t\t\t<view class=\"loading-text\">{{ loadingText }}</view>\r\n\t\t</view>\r\n\r\n\t\t<!-- 行程信息 -->\r\n\t\t<view v-else class=\"trip-info\">\r\n\t\t\t<view class=\"date-time\">{{ date }} {{ dateDesc }} {{ time }}出发 (约{{ duration }}小时)</view>\r\n\r\n\t\t\t<view class=\"route-info\">\r\n\t\t\t\t<view class=\"route-stations\">\r\n\t\t\t\t\t<text class=\"route-from\">{{ departure }}-{{ departureDoor }}</text>\r\n\t\t\t\t\t<text class=\"route-arrow\">→</text>\r\n\t\t\t\t\t<text class=\"route-to\">{{ arrival }}</text>\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<view class=\"bus-type\">{{ typeName }}</view>\r\n\t\t\t</view>\r\n\r\n\t\t\t<view class=\"trip-details\">\r\n\t\t\t\t<view class=\"trip-detail-item\" @click=\"showAddressDetail\">\r\n\t\t\t\t\t<view class=\"detail-info\">\r\n\t\t\t\t\t\t<text class=\"detail-label\">ⓘ 发车前24小时不可退票、改签</text>\r\n\t\t\t\t\t\t<!-- <text class=\"detail-separator\">|</text> -->\r\n\t\t\t\t\t\t<text class=\"detail-label\" style=\"text-align: right;padding-right: 10rpx;\">{{ departureLocation }}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\r\n\t\t<!-- 乘客选择 -->\r\n\t\t<view class=\"passenger-section\">\r\n\t\t\t<view class=\"section-title\">选择乘客</view>\r\n\r\n\t\t\t<view class=\"passenger-list\">\r\n\t\t\t\t<view v-for=\"(passenger, index) in passengerList\" :key=\"index\" class=\"passenger-item\"\r\n\t\t\t\t\t@click=\"selectPassenger(index)\">\r\n\t\t\t\t\t<!-- 选择框 -->\r\n\t\t\t\t\t<view class=\"passenger-checkbox\">\r\n\t\t\t\t\t\t<view :class=\"['checkbox-circle', { 'checkbox-active': passenger.selected }]\">\r\n\t\t\t\t\t\t\t<view v-if=\"passenger.selected\" class=\"checkbox-inner\"></view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t<!-- 乘客信息（合并在一行） -->\r\n\t\t\t\t\t<view class=\"passenger-info\">\r\n\t\t\t\t\t\t<text class=\"passenger-name\">{{ passenger.name }}</text>\r\n\t\t\t\t\t\t<text class=\"passenger-id\">{{ passenger.idCard }}</text>\r\n\t\t\t\t\t\t<text class=\"passenger-type-text\">{{ passenger.type }}</text>\r\n\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t<!-- 右箭头图标 -->\r\n\t\t\t\t\t<view class=\"passenger-arrow\" @click.stop=\"editPassenger(index)\">\r\n\t\t\t\t\t\t<image src=\"/static/icons/arrow_right.png\" class=\"arrow-icon\"></image>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<!-- 选择乘客按钮 -->\r\n\t\t\t\t<view class=\"add-passenger\" @click=\"goToPassengerManagement\">\r\n\t\t\t\t\t<image src=\"/static/icons/add_circle.png\" class=\"add-icon\"></image>\r\n\t\t\t\t\t<text class=\"add-text\">选择乘客</text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\r\n\t\t\t<!-- 乘客电话 -->\r\n\t\t\t<view class=\"passenger-phone\">\r\n\t\t\t\t<view class=\"phone-label\">乘客电话 <text class=\"required\"></text></view>\r\n\t\t\t\t<input\r\n\t\t\t\t\tclass=\"phone-input\"\r\n\t\t\t\t\ttype=\"number\"\r\n\t\t\t\t\tplaceholder=\"请输入联系电话\"\r\n\t\t\t\t\tv-model=\"contactPhone\"\r\n\t\t\t\t\tmaxlength=\"11\"\r\n\t\t\t\t/>\r\n\t\t\t</view>\r\n\r\n\t\t\t<!-- 备注信息 -->\r\n\t\t\t<view class=\"remark-section\">\r\n\t\t\t\t<view class=\"remark-label\">备注</view>\r\n\t\t\t\t<input\r\n\t\t\t\t\tclass=\"remark-input\"\r\n\t\t\t\t\tplaceholder=\"有其它特定要求请备注（选填）\"\r\n\t\t\t\t\tv-model=\"remark\"\r\n\t\t\t\t/>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\r\n\t\t<!-- 价格信息 -->\r\n\t\t<view class=\"price-section\">\r\n\t\t\t<view class=\"price-item\">\r\n\t\t\t\t<text class=\"price-label\">车票总价</text>\r\n\t\t\t\t<text class=\"price-value\">¥{{ totalPrice.toFixed(2) }}</text>\r\n\t\t\t</view>\r\n\r\n\t\t\t<view class=\"price-item\">\r\n\t\t\t\t<text class=\"price-label\">厂商优惠</text>\r\n\t\t\t\t<text class=\"price-discount\">减¥{{ discount.toFixed(2) }}</text>\r\n\t\t\t</view>\r\n\r\n\t\t\t<view class=\"price-item coupon-item\" @click=\"selectCoupon\">\r\n\t\t\t\t<text class=\"price-label\">优惠券</text>\r\n\t\t\t\t<view class=\"coupon-select\">\r\n\t\t\t\t\t<text class=\"coupon-text\" v-if=\"!selectedCoupon\">选择优惠券</text>\r\n\t\t\t\t\t<text class=\"coupon-selected\" v-else>-¥{{ couponDiscount.toFixed(2) }}</text>\r\n\t\t\t\t\t<image src=\"/static/icons/arrow_right.png\" class=\"arrow-icon\"></image>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\r\n\t\t\t<view class=\"total-price\">\r\n\t\t\t\t<text class=\"total-label\">合计</text>\r\n\t\t\t\t<text class=\"total-value\">¥{{ finalPrice.toFixed(2) }}</text>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\r\n\t\t<!-- 底部支付栏 -->\r\n\t\t<view class=\"payment-bar\">\r\n\t\t\t<!-- 购票须知 -->\r\n\t\t\t<view class=\"terms-section\">\r\n\t\t\t\t<view class=\"terms-checkbox\" @click=\"toggleTerms\">\r\n\t\t\t\t\t<view :class=\"['checkbox-circle', { 'checkbox-active': termsAgreed }]\">\r\n\t\t\t\t\t\t<view v-if=\"termsAgreed\" class=\"checkbox-inner\"></view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<text class=\"terms-text\">我已阅读并同意</text>\r\n\t\t\t\t<text class=\"terms-link\" @click.stop=\"viewTerms\">《购票须知》</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"payment-total\">\r\n\t\t\t\t<text class=\"payment-label\">共计</text>\r\n\t\t\t\t<text class=\"payment-currency\">¥</text>\r\n\t\t\t\t<text class=\"payment-value\">{{ finalPrice.toFixed(2) }}</text>\r\n\t\t\t</view>\r\n\r\n\t\t\t<view class=\"payment-button\" @click=\"confirmPayment\">立即支付</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<!-- 优惠券选择弹窗 -->\r\n\t\t<view class=\"coupon-popup\" v-if=\"showCouponPopup\">\r\n\t\t\t<view class=\"coupon-mask\" @click=\"closeCouponPopup\"></view>\r\n\t\t\t<view class=\"coupon-container\">\r\n\t\t\t\t<view class=\"coupon-header\">\r\n\t\t\t\t\t<text class=\"coupon-close\" @click=\"closeCouponPopup\">×</text>\r\n\t\t\t\t\t<text class=\"coupon-title\">使用优惠券</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t\r\n\t\t\t\t<view class=\"coupon-list\">\r\n\t\t\t\t\t<view v-for=\"(coupon, index) in couponList\" \r\n\t\t\t\t\t\t:key=\"coupon.id\" \r\n\t\t\t\t\t\tclass=\"coupon-item\"\r\n\t\t\t\t\t\t@click=\"chooseCoupon(index)\">\r\n\t\t\t\t\t\t<!-- 优惠券内容 -->\r\n\t\t\t\t\t\t<view class=\"coupon-content\">\r\n\t\t\t\t\t\t\t<!-- 左侧金额 -->\r\n\t\t\t\t\t\t\t<view class=\"coupon-amount\">\r\n\t\t\t\t\t\t\t\t<text class=\"coupon-currency\">¥</text>\r\n\t\t\t\t\t\t\t\t<text class=\"coupon-value\">{{ coupon.amount }}</text>\r\n\t\t\t\t\t\t\t\t<text v-if=\"coupon.condition\" class=\"coupon-condition\">{{ coupon.condition }}</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<!-- 右侧信息 -->\r\n\t\t\t\t\t\t\t<view class=\"coupon-info\">\r\n\t\t\t\t\t\t\t\t<text class=\"coupon-type\">{{ coupon.type }}</text>\r\n\t\t\t\t\t\t\t\t<text class=\"coupon-expire\">有效期至：{{ coupon.expireDate }}</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<!-- 选择状态 -->\r\n\t\t\t\t\t\t\t<view class=\"coupon-select-icon\" :class=\"{'selected': coupon.selected}\">\r\n\t\t\t\t\t\t\t\t<view v-if=\"coupon.selected\" class=\"select-inner\"></view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<!-- 锯齿边 -->\r\n\t\t\t\t\t\t<view class=\"coupon-edge-left\"></view>\r\n\t\t\t\t\t\t<view class=\"coupon-edge-right\"></view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t\r\n\t\t\t\t<view class=\"coupon-footer\">\r\n\t\t\t\t\t<view class=\"coupon-confirm-btn\" @click=\"confirmCoupon\">确认</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\nimport { ticketApi, configApi } from '@/utils/api.js';\r\nimport auth from '@/utils/auth.js';\r\nimport request from '@/utils/request.js';\r\n\r\nexport default {\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\t// 车票ID\r\n\t\t\tticketId: null,\r\n\r\n\t\t\t// 加载状态\r\n\t\t\tloading: false,\r\n\t\t\tloadingText: '加载中...',\r\n\r\n\t\t\t// 路线信息\r\n\t\t\tdeparture: '',\r\n\t\t\tdepartureDoor: '',\r\n\t\t\tarrival: '',\r\n\t\t\tdate: '',\r\n\t\t\tdateDesc: '',\r\n\t\t\ttime: '',\r\n\t\t\tduration: '',\r\n\t\t\ttypeName: '',\r\n\t\t\ttype: '',\r\n\t\t\tdepartureLocation: '',\r\n\r\n\t\t\t// 价格信息\r\n\t\t\toriginalPrice: 0,\r\n\t\t\tcurrentPrice: 0,\r\n\t\t\tdiscount: 0,\r\n\t\t\tfinalPrice: 0,\r\n\r\n\t\t\t// 乘客信息\r\n\t\t\tpassengerList: [], // 改为空数组，从乘客管理页面选择\r\n\t\t\tcontactPhone: '', // 联系电话（必填）\r\n\t\t\tremark: '', // 备注信息（选填）\r\n\r\n\t\t\t// 计算后的价格\r\n\t\t\ttotalPrice: 100,\r\n\r\n\t\t\t// 条款同意\r\n\t\t\ttermsAgreed: false,\r\n\t\t\t\r\n\t\t\t// 优惠券相关\r\n\t\t\tshowCouponPopup: false, // 是否显示优惠券弹窗\r\n\t\t\tcouponList: [], // 从接口获取的优惠券列表\r\n\t\t\tselectedCoupon: null, // 选中的优惠券\r\n\t\t\tcouponDiscount: 0 // 优惠券优惠金额\r\n\t\t};\r\n\t},\r\n\r\n\tonLoad(options) {\r\n\t\t// 从缓存获取用户手机号\r\n\t\tthis.loadUserContactInfo();\r\n\r\n\t\t// 加载优惠券列表\r\n\t\tthis.loadCouponList();\r\n\r\n\t\t// 从URL参数获取车票ID\r\n\t\tif (options && options.ticketId) {\r\n\t\t\tthis.ticketId = options.ticketId;\r\n\r\n\t\t\t// 先设置基本信息（从URL参数获取）\r\n\t\t\tthis.departure = decodeURIComponent(options.departure || '');\r\n\t\t\tthis.arrival = decodeURIComponent(options.arrival || '');\r\n\t\t\tthis.date = decodeURIComponent(options.date || '');\r\n\t\t\tthis.dateDesc = decodeURIComponent(options.dateDesc || '');\r\n\r\n\t\t\t// 设置导航栏标题\r\n\t\t\tif (this.departure && this.arrival) {\r\n\t\t\t\tuni.setNavigationBarTitle({\r\n\t\t\t\t\ttitle: `${this.departure} — ${this.arrival}`\r\n\t\t\t\t});\r\n\t\t\t}\r\n\r\n\t\t\t// 调用接口获取车票详细信息\r\n\t\t\tthis.loadTicketDetail();\r\n\t\t} else {\r\n\t\t\t// 如果没有车票ID，使用备用数据或默认值\r\n\t\t\tthis.loadBackupData(options);\r\n\t\t}\r\n\t},\r\n\r\n\tmethods: {\r\n\t\t// 从缓存加载用户联系信息\r\n\t\tloadUserContactInfo() {\r\n\t\t\ttry {\r\n\t\t\t\tconst userInfo = auth.getUserInfo();\r\n\t\t\t\tif (userInfo && userInfo.phone) {\r\n\t\t\t\t\t// 可以预填充用户手机号，但用户仍需确认\r\n\t\t\t\t\tthis.contactPhone = userInfo.phone;\r\n\t\t\t\t\tconsole.log('从缓存预填充用户手机号:', this.contactPhone);\r\n\t\t\t\t} else {\r\n\t\t\t\t\tconsole.warn('缓存中未找到用户手机号信息，需要用户手动输入');\r\n\t\t\t\t\tthis.contactPhone = '';\r\n\t\t\t\t}\r\n\t\t\t} catch (error) {\r\n\t\t\t\tconsole.error('获取用户联系信息失败:', error);\r\n\t\t\t\tthis.contactPhone = '';\r\n\t\t\t}\r\n\t\t},\r\n\r\n\t\t// 加载优惠券列表\r\n\t\tasync loadCouponList() {\r\n\t\t\ttry {\r\n\t\t\t\tconsole.log('开始加载优惠券列表...');\r\n\t\t\t\tconst response = await request.get('/app/coupon/user/list');\r\n\r\n\t\t\t\tif (response && response.code === 200 && response.data) {\r\n\t\t\t\t\t// 处理优惠券数据，只保留待使用的无门槛优惠券\r\n\t\t\t\t\tthis.couponList = this.processCouponsData(response.data);\r\n\t\t\t\t\tconsole.log('优惠券列表加载成功:', this.couponList);\r\n\t\t\t\t} else {\r\n\t\t\t\t\tconsole.warn('优惠券列表加载失败:', response);\r\n\t\t\t\t\tthis.couponList = [];\r\n\t\t\t\t}\r\n\t\t\t} catch (error) {\r\n\t\t\t\tconsole.error('加载优惠券列表失败:', error);\r\n\t\t\t\tthis.couponList = [];\r\n\t\t\t}\r\n\t\t},\r\n\r\n\t\t// 处理优惠券数据\r\n\t\tprocessCouponsData(data) {\r\n\t\t\treturn data.filter(item => {\r\n\t\t\t\t// 只保留待使用的优惠券 (type === 0)\r\n\t\t\t\treturn item.type === 0;\r\n\t\t\t}).map(item => {\r\n\t\t\t\t// 格式化时间\r\n\t\t\t\tconst endTime = this.formatDate(item.endTime);\r\n\r\n\t\t\t\treturn {\r\n\t\t\t\t\tid: item.id,\r\n\t\t\t\t\tamount: item.balancePice,\r\n\t\t\t\t\ttitle: item.couponName || '优惠券',\r\n\t\t\t\t\ttype: '无门槛', // 写死为无门槛类型\r\n\t\t\t\t\texpireDate: endTime,\r\n\t\t\t\t\tselected: false,\r\n\t\t\t\t\tcondition: '',\r\n\t\t\t\t\tcouponId: item.couponId\r\n\t\t\t\t};\r\n\t\t\t});\r\n\t\t},\r\n\r\n\t\t// 格式化日期\r\n\t\tformatDate(dateStr) {\r\n\t\t\tif (!dateStr) return '';\r\n\r\n\t\t\ttry {\r\n\t\t\t\tconst date = new Date(dateStr);\r\n\t\t\t\tconst year = date.getFullYear();\r\n\t\t\t\tconst month = String(date.getMonth() + 1).padStart(2, '0');\r\n\t\t\t\tconst day = String(date.getDate()).padStart(2, '0');\r\n\t\t\t\tconst hour = String(date.getHours()).padStart(2, '0');\r\n\t\t\t\tconst minute = String(date.getMinutes()).padStart(2, '0');\r\n\t\t\t\treturn `${year}.${month}.${day} ${hour}:${minute}`;\r\n\t\t\t} catch (error) {\r\n\t\t\t\tconsole.error('日期格式化失败:', error);\r\n\t\t\t\treturn dateStr;\r\n\t\t\t}\r\n\t\t},\r\n\r\n\t\t// 调用计算价格接口\r\n\t\tasync calculatePriceFromApi() {\r\n\t\t\ttry {\r\n\t\t\t\t// 计算选中的乘客数量\r\n\t\t\t\tconst selectedCount = this.passengerList.filter(item => item.selected).length;\r\n\r\n\t\t\t\tif (selectedCount === 0) {\r\n\t\t\t\t\tconsole.log('没有选中的乘客，跳过价格计算');\r\n\t\t\t\t\t// 重置价格信息\r\n\t\t\t\t\tthis.totalPrice = 0;\r\n\t\t\t\t\tthis.discount = 0;\r\n\t\t\t\t\tthis.finalPrice = 0;\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\r\n\t\t\t\tconsole.log('selectedCoupon', this.selectedCoupon);\r\n\t\t\t\tconst requestData = {\r\n\t\t\t\t\tticketId: this.ticketId,\r\n\t\t\t\t\tnumber: selectedCount,\r\n\t\t\t\t\tcouponId: this.selectedCoupon ? this.selectedCoupon.couponId : null\r\n\t\t\t\t};\r\n\r\n\t\t\t\tconsole.log('调用计算价格接口，参数:', requestData);\r\n\r\n\t\t\t\tconst response = await request.post('/app/order/calculate/price', requestData);\r\n\r\n\t\t\t\tif (response && response.code === 200 && response.data) {\r\n\t\t\t\t\tconsole.log('价格计算接口响应:', response);\r\n\r\n\t\t\t\t\t// 更新价格信息\r\n\t\t\t\t\tconst data = response.data;\r\n\t\t\t\t\tthis.totalPrice = data.originalPrice || 0; // 车票总价\r\n\t\t\t\t\tthis.discount = data.discountPrice || 0; // 厂商优惠\r\n\t\t\t\t\tthis.finalPrice = data.number || 0; // 合计金额\r\n\r\n\t\t\t\t\t// 计算优惠券优惠金额（总价 - 厂商优惠 - 合计 = 优惠券优惠）\r\n\t\t\t\t\tthis.couponDiscount = this.totalPrice - this.discount - this.finalPrice;\r\n\t\t\t\t\tif (this.couponDiscount < 0) {\r\n\t\t\t\t\t\tthis.couponDiscount = 0;\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\tconsole.log('价格更新完成:', {\r\n\t\t\t\t\t\ttotalPrice: this.totalPrice,\r\n\t\t\t\t\t\tdiscount: this.discount,\r\n\t\t\t\t\t\tcouponDiscount: this.couponDiscount,\r\n\t\t\t\t\t\tfinalPrice: this.finalPrice\r\n\t\t\t\t\t});\r\n\t\t\t\t} else {\r\n\t\t\t\t\tconsole.warn('价格计算接口调用失败:', response);\r\n\t\t\t\t}\r\n\t\t\t} catch (error) {\r\n\t\t\t\tconsole.error('调用价格计算接口失败:', error);\r\n\t\t\t}\r\n\t\t},\r\n\r\n\t\t// 加载车票详细信息\r\n\t\tasync loadTicketDetail() {\r\n\t\t\ttry {\r\n\t\t\t\tthis.loading = true;\r\n\t\t\t\tthis.loadingText = '正在加载车票信息...';\r\n\r\n\t\t\t\tconsole.log('获取车票详情，ID:', this.ticketId);\r\n\r\n\t\t\t\t// 调用接口获取车票详细信息\r\n\t\t\t\tconst response = await ticketApi.getTicketById(this.ticketId);\r\n\r\n\t\t\t\tif (response && response.data) {\r\n\t\t\t\t\t// 处理接口返回的数据\r\n\t\t\t\t\tthis.processTicketDetail(response.data);\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthrow new Error('获取车票信息失败');\r\n\t\t\t\t}\r\n\r\n\t\t\t} catch (error) {\r\n\t\t\t\tconsole.error('获取车票详情失败:', error);\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: '获取车票信息失败',\r\n\t\t\t\t\ticon: 'none'\r\n\t\t\t\t});\r\n\r\n\t\t\t\t// 如果接口调用失败，尝试使用备用数据\r\n\t\t\t\tthis.loadBackupDataFromUrl();\r\n\t\t\t} finally {\r\n\t\t\t\tthis.loading = false;\r\n\t\t\t}\r\n\t\t},\r\n\r\n\t\t// 处理车票详细信息\r\n\t\tprocessTicketDetail(data) {\r\n\t\t\t// 使用与ticket_list页面相同的时间提取方法\r\n\t\t\tthis.time = this.extractTimeFromDateTime(data.departureTime);\r\n\t\t\tthis.typeName = data.carType;\r\n\t\t\tthis.type = this.getTicketType(data.vehicleType);\r\n\t\t\tthis.departureDoor = data.departureDoor ;\r\n\t\t\tthis.duration = data.travelTime;\r\n\t\t\tthis.originalPrice = parseFloat(data.originalPrice || 0);\r\n\t\t\tthis.currentPrice = parseFloat(data.currentPrice || data.price || 0);\r\n\t\t\tthis.discount = parseFloat(data.discount || 0);\r\n\t\t\tthis.departureLocation = data.departureLocation;\r\n\r\n\t\t\t// 计算价格\r\n\t\t\tthis.calculatePrice();\r\n\r\n\t\t\tconsole.log('车票详情处理完成:', {\r\n\t\t\t\ttime: this.time,\r\n\t\t\t\ttypeName: this.typeName,\r\n\t\t\t\toriginalPrice: this.originalPrice,\r\n\t\t\t\tcurrentPrice: this.currentPrice\r\n\t\t\t});\r\n\t\t},\r\n\r\n\t\t// 从完整的日期时间字符串中提取时分（与ticket_list页面保持一致）\r\n\t\textractTimeFromDateTime(dateTimeString) {\r\n\t\t\tif (!dateTimeString) {\r\n\t\t\t\treturn '08:00'; // 默认时间\r\n\t\t\t}\r\n\r\n\t\t\ttry {\r\n\t\t\t\tlet timeStr = '';\r\n\r\n\t\t\t\tif (dateTimeString.includes(' ')) {\r\n\t\t\t\t\t// 格式: \"2025-06-10 00:00:00\"\r\n\t\t\t\t\ttimeStr = dateTimeString.split(' ')[1];\r\n\t\t\t\t} else if (dateTimeString.includes('T')) {\r\n\t\t\t\t\t// 格式: \"2025-06-10T00:00:00\"\r\n\t\t\t\t\ttimeStr = dateTimeString.split('T')[1];\r\n\t\t\t\t} else {\r\n\t\t\t\t\t// 如果已经是时间格式，直接返回\r\n\t\t\t\t\treturn dateTimeString;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t// 提取时分部分 (HH:MM)\r\n\t\t\t\tif (timeStr && timeStr.length >= 5) {\r\n\t\t\t\t\treturn timeStr.substring(0, 5); // 取前5位 \"HH:MM\"\r\n\t\t\t\t}\r\n\r\n\t\t\t\treturn '08:00'; // 默认时间\r\n\t\t\t} catch (error) {\r\n\t\t\t\tconsole.error('解析发车时间失败:', dateTimeString, error);\r\n\t\t\t\treturn '08:00'; // 默认时间\r\n\t\t\t}\r\n\t\t},\r\n\r\n\t\t// 根据车辆类型获取对应的样式类型（与ticket_list页面保持一致）\r\n\t\tgetTicketType(vehicleType) {\r\n\t\t\tswitch (vehicleType) {\r\n\t\t\t\tcase 1:\r\n\t\t\t\tcase '商务车':\r\n\t\t\t\t\treturn 'business';\r\n\t\t\t\tcase 2:\r\n\t\t\t\tcase '普通车':\r\n\t\t\t\tdefault:\r\n\t\t\t\t\treturn 'normal';\r\n\t\t\t}\r\n\t\t},\r\n\r\n\t\t// 加载备用数据（当没有车票ID时使用）\r\n\t\tloadBackupData(options) {\r\n\t\t\t// 默认值\r\n\t\t\tconst defaults = {\r\n\t\t\t\tdeparture: '同济大学',\r\n\t\t\t\tdepartureDoor: '南门',\r\n\t\t\t\tarrival: '泰安各县城',\r\n\t\t\t\tdate: '05月14日',\r\n\t\t\t\tdateDesc: '明天',\r\n\t\t\t\ttime: '07:50',\r\n\t\t\t\tduration: '约3小时',\r\n\t\t\t\ttypeName: '商务车',\r\n\t\t\t\ttype: 'business',\r\n\t\t\t\toriginalPrice: 100.00,\r\n\t\t\t\tcurrentPrice: 98.00,\r\n\t\t\t\tdiscount: 2.00\r\n\t\t\t};\r\n\r\n\t\t\t// 使用URL参数或默认值\r\n\t\t\tif (options && Object.keys(options).length > 0) {\r\n\t\t\t\tthis.departure = decodeURIComponent(options.departure || defaults.departure);\r\n\t\t\t\tthis.departureDoor = decodeURIComponent(options.departureDoor || defaults.departureDoor);\r\n\t\t\t\tthis.arrival = decodeURIComponent(options.arrival || defaults.arrival);\r\n\t\t\t\tthis.date = decodeURIComponent(options.date || defaults.date);\r\n\t\t\t\tthis.dateDesc = decodeURIComponent(options.dateDesc || defaults.dateDesc);\r\n\t\t\t\tthis.time = decodeURIComponent(options.time || defaults.time);\r\n\t\t\t\tthis.duration = decodeURIComponent(options.duration || defaults.duration);\r\n\t\t\t\tthis.typeName = decodeURIComponent(options.typeName || defaults.typeName);\r\n\t\t\t\tthis.type = options.type || defaults.type;\r\n\t\t\t\tthis.originalPrice = parseFloat(options.originalPrice) || defaults.originalPrice;\r\n\t\t\t\tthis.currentPrice = parseFloat(options.currentPrice) || defaults.currentPrice;\r\n\t\t\t\tthis.discount = parseFloat(options.discount) || defaults.discount;\r\n\t\t\t} else {\r\n\t\t\t\t// 使用默认值\r\n\t\t\t\tObject.assign(this, defaults);\r\n\t\t\t}\r\n\r\n\t\t\t// 设置导航栏标题\r\n\t\t\tuni.setNavigationBarTitle({\r\n\t\t\t\ttitle: `${this.departure} — ${this.arrival}`\r\n\t\t\t});\r\n\r\n\t\t\t// 计算价格\r\n\t\t\tthis.calculatePrice();\r\n\t\t},\r\n\r\n\t\t// 从URL参数加载备用数据（当接口调用失败时使用）\r\n\t\tloadBackupDataFromUrl() {\r\n\t\t\t// 尝试从当前页面的URL参数中获取备用数据\r\n\t\t\tconst pages = getCurrentPages();\r\n\t\t\tconst currentPage = pages[pages.length - 1];\r\n\t\t\tconst options = currentPage.options;\r\n\r\n\t\t\tif (options) {\r\n\t\t\t\tthis.departureDoor = decodeURIComponent(options.departureDoor || '南门');\r\n\t\t\t\tthis.time = decodeURIComponent(options.time || '08:00');\r\n\t\t\t\tthis.duration = decodeURIComponent(options.duration || '约6小时');\r\n\t\t\t\tthis.typeName = decodeURIComponent(options.typeName || '普通车');\r\n\t\t\t\tthis.type = options.type || 'normal';\r\n\t\t\t\tthis.originalPrice = parseFloat(options.originalPrice) || 100;\r\n\t\t\t\tthis.currentPrice = parseFloat(options.currentPrice) || 98;\r\n\t\t\t\tthis.discount = parseFloat(options.discount) || 2;\r\n\r\n\t\t\t\t// 计算价格\r\n\t\t\t\tthis.calculatePrice();\r\n\t\t\t}\r\n\t\t},\r\n\r\n\t\t// 显示地址详情\r\n\t\tshowAddressDetail() {\r\n\t\t\t// 实现显示地址详情的逻辑\r\n\t\t},\r\n\r\n\t\t// 选择乘客\r\n\t\tselectPassenger(index) {\r\n\t\t\t// 如果有乘客数据，切换选中状态\r\n\t\t\tif (this.passengerList.length > 0) {\r\n\t\t\t\tthis.passengerList[index].selected = !this.passengerList[index].selected;\r\n\t\t\t\tthis.calculatePrice();\r\n\t\t\t\t// 调用价格计算接口\r\n\t\t\t\tthis.calculatePriceFromApi();\r\n\t\t\t} else {\r\n\t\t\t\t// 如果没有乘客数据，跳转到乘客管理页面\r\n\t\t\t\tthis.goToPassengerManagement();\r\n\t\t\t}\r\n\t\t},\r\n\r\n\t\t// 跳转到乘客管理页面\r\n\t\tgoToPassengerManagement() {\r\n\t\t\tuni.navigateTo({\r\n\t\t\t\turl: '/subpkg-user/passenger_management/passenger_management'\r\n\t\t\t});\r\n\t\t},\r\n\r\n\t\t// 编辑乘客信息\r\n\t\teditPassenger(index) {\r\n\t\t\t// 获取要编辑的乘客信息\r\n\t\t\tconst passenger = this.passengerList[index];\r\n\r\n\t\t\t// 跳转到编辑乘客页面\r\n\t\t\tuni.navigateTo({\r\n\t\t\t\turl: '/subpkg-user/passenger_edit/passenger_edit',\r\n\t\t\t\tsuccess: (res) => {\r\n\t\t\t\t\t// 传递乘客数据\r\n\t\t\t\t\tres.eventChannel.emit('acceptPassengerData', {\r\n\t\t\t\t\t\tindex: index,\r\n\t\t\t\t\t\tpassenger: passenger\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t},\r\n\r\n\t\t// 添加乘客\r\n\t\taddPassenger() {\r\n\t\t\t// 跳转到编辑乘客页面，不传递乘客数据表示新增\r\n\t\t\tuni.navigateTo({\r\n\t\t\t\turl: '/subpkg-user/passenger_edit/passenger_edit'\r\n\t\t\t});\r\n\t\t},\r\n\r\n\t\t// 添加新乘客到列表（供编辑页面调用）\r\n\t\taddNewPassenger(passenger) {\r\n\t\t\tthis.passengerList.push(passenger);\r\n\t\t\t// 重新计算价格\r\n\t\t\tthis.calculatePrice();\r\n\t\t\t// 调用价格计算接口\r\n\t\t\tthis.calculatePriceFromApi();\r\n\t\t},\r\n\r\n\t\t// 更新已有乘客（供编辑页面调用）\r\n\t\tupdatePassenger(index, passenger) {\r\n\t\t\tif (index >= 0 && index < this.passengerList.length) {\r\n\t\t\t\tthis.passengerList[index] = passenger;\r\n\t\t\t\t// 重新计算价格\r\n\t\t\t\tthis.calculatePrice();\r\n\t\t\t\t// 调用价格计算接口\r\n\t\t\t\tthis.calculatePriceFromApi();\r\n\t\t\t}\r\n\t\t},\r\n\r\n\t\t// 移除乘客（供编辑页面调用）\r\n\t\tremovePassenger(index) {\r\n\t\t\tif (index >= 0 && index < this.passengerList.length) {\r\n\t\t\t\tthis.passengerList.splice(index, 1);\r\n\t\t\t\t// 重新计算价格\r\n\t\t\t\tthis.calculatePrice();\r\n\t\t\t\t// 调用价格计算接口\r\n\t\t\t\tthis.calculatePriceFromApi();\r\n\t\t\t}\r\n\t\t},\r\n\r\n\t\t// 接收从乘客管理页面选择的乘客\r\n\t\treceiveSelectedPassengers(selectedPassengers) {\r\n\t\t\t// 将选中的乘客添加到乘客列表中\r\n\t\t\tselectedPassengers.forEach(passenger => {\r\n\t\t\t\t// 检查是否已存在（根据ID或身份证号判断）\r\n\t\t\t\tconst exists = this.passengerList.some(p =>\r\n\t\t\t\t\tp.id === passenger.id || p.idCard === passenger.idCard\r\n\t\t\t\t);\r\n\r\n\t\t\t\tif (!exists) {\r\n\t\t\t\t\t// 添加乘客，设置为选中状态\r\n\t\t\t\t\tthis.passengerList.push({\r\n\t\t\t\t\t\t...passenger,\r\n\t\t\t\t\t\tselected: true,\r\n\t\t\t\t\t\ttype: '成人票' // 默认票型\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\t\t\t});\r\n\r\n\t\t\t// 重新计算价格\r\n\t\t\tthis.calculatePrice();\r\n\t\t\t// 调用价格计算接口\r\n\t\t\tthis.calculatePriceFromApi();\r\n\r\n\t\t\t// 提示用户\r\n\t\t\tuni.showToast({\r\n\t\t\t\ttitle: `已选择${selectedPassengers.length}位乘客`,\r\n\t\t\t\ticon: 'success'\r\n\t\t\t});\r\n\t\t},\r\n\r\n\t\t// 选择优惠券\r\n\t\tselectCoupon() {\r\n\t\t\t// 显示优惠券弹窗\r\n\t\t\tthis.showCouponPopup = true;\r\n\t\t\t\r\n\t\t\t// 初始化选中的优惠券\r\n\t\t\tthis.selectedCoupon = this.couponList.find(item => item.selected) || null;\r\n\t\t},\r\n\r\n\t\t// 关闭优惠券弹窗\r\n\t\tcloseCouponPopup() {\r\n\t\t\tthis.showCouponPopup = false;\r\n\t\t},\r\n\r\n\t\t// 选择某个优惠券\r\n\t\tchooseCoupon(index) {\r\n\t\t\tconst currentCoupon = this.couponList[index];\r\n\r\n\t\t\t// 如果当前优惠券已经选中，则取消选择\r\n\t\t\tif (currentCoupon.selected) {\r\n\t\t\t\tcurrentCoupon.selected = false;\r\n\t\t\t\tthis.selectedCoupon = null;\r\n\t\t\t} else {\r\n\t\t\t\t// 取消所有优惠券的选中状态\r\n\t\t\t\tthis.couponList.forEach(item => {\r\n\t\t\t\t\titem.selected = false;\r\n\t\t\t\t});\r\n\r\n\t\t\t\t// 设置当前优惠券为选中状态\r\n\t\t\t\tcurrentCoupon.selected = true;\r\n\t\t\t\tthis.selectedCoupon = currentCoupon;\r\n\t\t\t}\r\n\r\n\t\t\tconsole.log('选择的优惠券:', this.selectedCoupon);\r\n\t\t},\r\n\r\n\t\t// 确认选择优惠券\r\n\t\tconfirmCoupon() {\r\n\t\t\t// 如果没有选择优惠券，重置优惠券相关数据\r\n\t\t\tif (!this.selectedCoupon) {\r\n\t\t\t\tthis.couponDiscount = 0;\r\n\t\t\t}\r\n\r\n\t\t\t// 调用价格计算接口（接口会返回实际的优惠金额）\r\n\t\t\tthis.calculatePriceFromApi();\r\n\r\n\t\t\t// 关闭弹窗\r\n\t\t\tthis.closeCouponPopup();\r\n\t\t},\r\n\r\n\t\t// 切换条款同意状态\r\n\t\ttoggleTerms() {\r\n\t\t\tthis.termsAgreed = !this.termsAgreed;\r\n\t\t},\r\n\r\n\t\t// 查看购票须知\r\n\t\tasync viewTerms() {\r\n\t\t\ttry {\r\n\t\t\t\t// 显示加载中\r\n\t\t\t\tuni.showLoading({\r\n\t\t\t\t\ttitle: '加载中...'\r\n\t\t\t\t});\r\n\r\n\t\t\t\t// 调用配置接口获取购票须知内容\r\n\t\t\t\tconst response = await configApi.getConfig('bay_ticket');\r\n\r\n\t\t\t\tuni.hideLoading();\r\n\r\n\t\t\t\tif (response && response.code === 200) {\r\n\t\t\t\t\t// 使用弹窗展示购票须知内容\r\n\t\t\t\t\tuni.showModal({\r\n\t\t\t\t\t\ttitle: '购票须知',\r\n\t\t\t\t\t\tcontent: response.msg || '暂无内容',\r\n\t\t\t\t\t\tshowCancel: false,\r\n\t\t\t\t\t\tconfirmText: '我知道了'\r\n\t\t\t\t\t});\r\n\t\t\t\t} else {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '获取购票须知失败',\r\n\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\t\t\t} catch (error) {\r\n\t\t\t\tuni.hideLoading();\r\n\t\t\t\tconsole.error('获取购票须知失败:', error);\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: '获取购票须知失败',\r\n\t\t\t\t\ticon: 'none'\r\n\t\t\t\t});\r\n\t\t\t}\r\n\t\t},\r\n\r\n\t\t// 确认支付\r\n\t\tasync confirmPayment() {\r\n\t\t\t// 验证条款同意\r\n\t\t\tif (!this.termsAgreed) {\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: '请阅读并同意《购票须知》',\r\n\t\t\t\t\ticon: 'none'\r\n\t\t\t\t});\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\r\n\t\t\t// 检查是否选择了乘客\r\n\t\t\tconst selectedPassengers = this.passengerList.filter(item => item.selected);\r\n\t\t\tif (selectedPassengers.length === 0) {\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: '请选择乘车人',\r\n\t\t\t\t\ticon: 'none'\r\n\t\t\t\t});\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\r\n\t\t\t// 验证联系电话（必填）\r\n\t\t\tif (!this.contactPhone || this.contactPhone.trim() === '') {\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: '请输入联系电话',\r\n\t\t\t\t\ticon: 'none'\r\n\t\t\t\t});\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\r\n\t\t\t// 验证电话格式\r\n\t\t\tconst phoneRegex = /^1[3-9]\\d{9}$/;\r\n\t\t\tif (!phoneRegex.test(this.contactPhone)) {\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: '请输入正确的手机号码',\r\n\t\t\t\t\ticon: 'none'\r\n\t\t\t\t});\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\r\n\t\t\ttry {\r\n\t\t\t\tuni.showLoading({\r\n\t\t\t\t\ttitle: '正在创建订单...',\r\n\t\t\t\t\tmask: true\r\n\t\t\t\t});\r\n\r\n\t\t\t\t// 调用订单创建接口\r\n\t\t\t\tconst orderData = {\r\n\t\t\t\t\tticketId: this.ticketId,\r\n\t\t\t\t\tcouponId: this.selectedCoupon ? this.selectedCoupon.id : null,\r\n\t\t\t\t\tphone: parseInt(this.contactPhone), // 转换为Long类型\r\n\t\t\t\t\tremark: this.remark || '', // 备注信息，可为空\r\n\t\t\t\t\tsysPassengerInformations: selectedPassengers.map(passenger => ({\r\n\t\t\t\t\t\tid: passenger.id,\r\n\t\t\t\t\t\tname: passenger.name,\r\n\t\t\t\t\t\tidNumber: passenger.rawIdNumber || passenger.idCard,\r\n\t\t\t\t\t\t// 如果有其他字段，可以在这里添加\r\n\t\t\t\t\t}))\r\n\t\t\t\t};\r\n\r\n\t\t\t\tconsole.log('创建订单请求参数:', orderData);\r\n\r\n\t\t\t\tconst response = await request.post('/app/order', orderData);\r\n\r\n\t\t\t\tif (response && response.code === 200) {\r\n\t\t\t\t\tconsole.log('订单创建成功:', response);\r\n\r\n\t\t\t\t\t// 检查是否有支付参数\r\n\t\t\t\t\tif (response.data && response.data.appid) {\r\n\t\t\t\t\t\tuni.showLoading({\r\n\t\t\t\t\t\t\ttitle: '正在发起支付...',\r\n\t\t\t\t\t\t\tmask: true\r\n\t\t\t\t\t\t});\r\n\r\n\t\t\t\t\t\t// 调用微信支付\r\n\t\t\t\t\t\tthis.callWechatPayWithParams(response.data);\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: '订单创建成功',\r\n\t\t\t\t\t\t\ticon: 'success'\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthrow new Error(response.msg || '订单创建失败');\r\n\t\t\t\t}\r\n\t\t\t} catch (error) {\r\n\t\t\t\tuni.hideLoading();\r\n\t\t\t\tconsole.error('订单创建失败:', error);\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: error.message || '订单创建失败，请重试',\r\n\t\t\t\t\ticon: 'none'\r\n\t\t\t\t});\r\n\t\t\t}\r\n\t\t},\r\n\r\n\t\t// 使用后端返回的完整参数调用微信支付\r\n\t\tcallWechatPayWithParams(payData) {\r\n\t\t\tuni.hideLoading();\r\n\r\n\t\t\tconsole.log('调用微信支付，参数:', payData);\r\n\r\n\t\t\t// 构建支付参数\r\n\t\t\tconst paymentParams = {\r\n\t\t\t\tprovider: 'wxpay',\r\n\t\t\t\tappid: payData.appid,\r\n\t\t\t\ttimeStamp: payData.timeStamp,\r\n\t\t\t\tnonceStr: payData.nonceStr,\r\n\t\t\t\tpackage: payData.prepayId, // 注意：这里使用prepayId字段\r\n\t\t\t\tsignType: payData.signType,\r\n\t\t\t\tpaySign: payData.paySign\r\n\t\t\t};\r\n\r\n\t\t\tconsole.log('微信支付参数:', paymentParams);\r\n\r\n\t\t\t// 调用微信支付\r\n\t\t\tuni.requestPayment({\r\n\t\t\t\t...paymentParams,\r\n\t\t\t\tsuccess: (res) => {\r\n\t\t\t\t\tconsole.log('支付成功:', res);\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '支付成功',\r\n\t\t\t\t\t\ticon: 'success'\r\n\t\t\t\t\t});\r\n\r\n\t\t\t\t\t// 跳转到购票记录页面，并传递支付成功标识\r\n\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\t\turl: '/pages/record/record?paymentSuccess=true'\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}, 1500);\r\n\t\t\t\t},\r\n\t\t\t\tfail: (err) => {\r\n\t\t\t\t\tconsole.error('支付失败:', err);\r\n\t\t\t\t\tlet errorMsg = '支付失败，请重试';\r\n\r\n\t\t\t\t\tif (err.errMsg) {\r\n\t\t\t\t\t\tif (err.errMsg.includes('cancel')) {\r\n\t\t\t\t\t\t\terrorMsg = '支付已取消';\r\n\t\t\t\t\t\t} else if (err.errMsg.includes('fail')) {\r\n\t\t\t\t\t\t\terrorMsg = '支付失败，请检查网络或重试';\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: errorMsg,\r\n\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t},\r\n\r\n\t\t// 调用微信支付（旧方法，保留用于测试）\r\n\t\tcallWechatPay(packageValue) {\r\n\t\t\t// 生成时间戳（秒）\r\n\t\t\tconst timeStamp = Math.floor(Date.now() / 1000).toString();\r\n\r\n\t\t\t// 生成随机字符串\r\n\t\t\tconst nonceStr = this.generateNonceStr();\r\n\r\n\t\t\t// 小程序appid\r\n\t\t\tconst appId = 'wx01c1e1d1bfcd4145';\r\n\r\n\t\t\tuni.hideLoading();\r\n\r\n\t\t\tconsole.log('调用微信支付，参数:', {\r\n\t\t\t\tappId: appId,\r\n\t\t\t\ttimeStamp: timeStamp,\r\n\t\t\t\tnonceStr: nonceStr,\r\n\t\t\t\tpackage: packageValue,\r\n\t\t\t\tsignType: 'MD5'\r\n\t\t\t});\r\n\r\n\t\t\t// 调用微信支付\r\n\t\t\tuni.requestPayment({\r\n\t\t\t\tprovider: 'wxpay',\r\n\t\t\t\t\"appid\": \"\",\r\n\t\t\t\ttimeStamp: \"\",\r\n\t\t\t\tnonceStr: \"\",\r\n\t\t\t\tpackage: \"\",\r\n\t\t\t\tsignType: '',\r\n\t\t\t\tpaySign: '', \r\n\t\t\t\tsuccess: (res) => {\r\n\t\t\t\t\tconsole.log('支付成功:', res);\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '支付成功',\r\n\t\t\t\t\t\ticon: 'success'\r\n\t\t\t\t\t});\r\n\r\n\t\t\t\t\t// 跳转到购票记录页面，并传递支付成功标识\r\n\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\tuni.reLaunch({\r\n\t\t\t\t\t\t\turl: '/pages/record/record?paymentSuccess=true'\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}, 1500);\r\n\t\t\t\t},\r\n\t\t\t\tfail: (err) => {\r\n\t\t\t\t\tconsole.error('支付失败:', err);\r\n\t\t\t\t\tlet errorMsg = '支付失败，请重试';\r\n\r\n\t\t\t\t\tif (err.errMsg) {\r\n\t\t\t\t\t\tif (err.errMsg.includes('cancel')) {\r\n\t\t\t\t\t\t\terrorMsg = '支付已取消';\r\n\t\t\t\t\t\t} else if (err.errMsg.includes('fail')) {\r\n\t\t\t\t\t\t\terrorMsg = '支付失败，请检查网络或重试';\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: errorMsg,\r\n\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t},\r\n\r\n\t\t// 生成随机字符串\r\n\t\tgenerateNonceStr() {\r\n\t\t\tconst chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';\r\n\t\t\tlet result = '';\r\n\t\t\tfor (let i = 0; i < 32; i++) {\r\n\t\t\t\tresult += chars.charAt(Math.floor(Math.random() * chars.length));\r\n\t\t\t}\r\n\t\t\treturn result;\r\n\t\t},\r\n\r\n\t\t// 计算价格（本地计算，主要用于初始化和备用）\r\n\t\tcalculatePrice() {\r\n\t\t\t// 计算选中的乘客数量\r\n\t\t\tconst selectedCount = this.passengerList.filter(item => item.selected).length;\r\n\r\n\t\t\t// 如果没有选中乘客，重置价格\r\n\t\t\tif (selectedCount === 0) {\r\n\t\t\t\tthis.totalPrice = 0;\r\n\t\t\t\tthis.discount = 0;\r\n\t\t\t\tthis.couponDiscount = 0;\r\n\t\t\t\tthis.finalPrice = 0;\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\r\n\t\t\t// 简单的本地计算（作为备用，主要依赖接口返回的数据）\r\n\t\t\tthis.totalPrice = this.originalPrice * selectedCount;\r\n\t\t\tthis.discount = 2.00 * selectedCount;\r\n\r\n\t\t\t// 考虑优惠券优惠（使用实际计算出的优惠金额）\r\n\t\t\tconst finalPrice = this.totalPrice - this.discount - this.couponDiscount;\r\n\t\t\tthis.finalPrice = finalPrice > 0 ? finalPrice : 0;\r\n\t\t}\r\n\t}\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\npage {\r\n\tbackground-color: #F5F5F5;\r\n}\r\n\r\n.content {\r\n\t// min-height: 100vh;\r\n\tdisplay: flex;\r\n\tflex-direction: column;\r\n\tpadding-bottom: 180rpx;\r\n\t/* 为底部的购票须知和支付栏预留空间 */\r\n\t// padding-top: 10rpx;\r\n\tbackground-color: #f5f7fa;\r\n\t/* 添加顶部边距 */\r\n}\r\n\r\n/* 加载状态 */\r\n.loading-container {\r\n\tdisplay: flex;\r\n\tjustify-content: center;\r\n\talign-items: center;\r\n\tpadding: 200rpx 0;\r\n\tmin-height: 50vh;\r\n}\r\n\r\n.loading-text {\r\n\tfont-size: 28rpx;\r\n\tcolor: #999;\r\n}\r\n\r\n/* 行程信息样式 */\r\n.trip-info {\r\n\tbackground-color: #fff;\r\n\tpadding: 34rpx 24rpx;\r\n\tmargin-bottom: 24rpx;\r\n\t// border-radius: 16rpx;\r\n\t// margin: 24rpx;\r\n\t// box-shadow: 0 6rpx 20rpx rgba(0, 0, 0, 0.1), 0 2rpx 5rpx rgba(0, 0, 0, 0.1);\r\n\t// border: 1rpx solid rgba(0, 0, 0, 0.03);\r\n}\r\n\r\n.date-time {\r\n\tfont-size: 24rpx;\r\n\t// font-weight: bold;\r\n\tcolor: #303133;\r\n\tpadding-bottom: 20rpx;\r\n\t// border-bottom: 1px solid #f0f0f0;\r\n}\r\n\r\n.route-info {\r\n\tdisplay: flex;\r\n\tjustify-content: space-between;\r\n\talign-items: center;\r\n\tpadding: 30rpx 0;\r\n\tborder-bottom: 1px solid #f0f0f0;\r\n}\r\n\r\n.route-stations {\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n}\r\n\r\n.route-from,\r\n.route-to {\r\n\tfont-size: 32rpx;\r\n\tcolor: #333;\r\n\tfont-weight: bold;\r\n}\r\n\r\n.route-arrow {\r\n\tmargin: 0 10rpx;\r\n\tcolor: #999;\r\n}\r\n\r\n.bus-type {\r\n\tfont-size: 26rpx;\r\n\tcolor: #000000;\r\n\t// background-color: #f5f7fa;\r\n\tpadding: 6rpx 16rpx;\r\n\tfont-weight: bold;\r\n\t// border-radius: 6rpx;\r\n}\r\n\r\n.trip-details {\r\n\tpadding-top: 20rpx;\r\n}\r\n\r\n.trip-detail-item {\r\n\tdisplay: flex;\r\n\tjustify-content: space-between;\r\n\talign-items: center;\r\n\tpadding: 16rpx 0;\r\n}\r\n\r\n.detail-info {\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tflex-wrap: wrap;\r\n\twidth: 100%;\r\n}\r\n\r\n.detail-label {\r\n\twidth: 50%;\r\n\tfont-size: 24rpx;\r\n\tcolor: #303133;\r\n\twhite-space: nowrap;\r\n\toverflow: hidden;\r\n\tbox-sizing: border-box;\r\n}\r\n\r\n.detail-separator {\r\n\tfont-size: 22rpx;\r\n\tcolor: #ddd;\r\n\tmargin: 0 10rpx;\r\n\tpadding-top: 2rpx;\r\n}\r\n\r\n.arrow-icon {\r\n\twidth: 32rpx;\r\n\theight: 32rpx;\r\n}\r\n\r\n/* 乘客选择样式 */\r\n.passenger-section {\r\n\tbackground-color: #fff;\r\n\tpadding: 34rpx 24rpx;\r\n\tmargin-bottom: 24rpx;\r\n\tborder-radius: 16rpx;\r\n\tmargin: 0 24rpx 24rpx 24rpx;\r\n\t// box-shadow: 0 6rpx 20rpx rgba(0, 0, 0, 0.1), 0 2rpx 5rpx rgba(0, 0, 0, 0.1);\r\n\t// border: 1rpx solid rgba(0, 0, 0, 0.03);\r\n}\r\n\r\n.section-title {\r\n\tfont-size: 32rpx;\r\n\tfont-weight: bold;\r\n\tcolor: #333;\r\n\tmargin-bottom: 20rpx;\r\n}\r\n\r\n.passenger-item {\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tpadding: 20rpx 0;\r\n\tborder-bottom: 1px solid #f0f0f0;\r\n}\r\n\r\n.passenger-checkbox {\r\n\tmargin-right: 20rpx;\r\n}\r\n\r\n.checkbox-circle {\r\n\twidth: 40rpx;\r\n\theight: 40rpx;\r\n\tborder-radius: 50%;\r\n\tborder: 2rpx solid #ddd;\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tjustify-content: center;\r\n}\r\n\r\n.checkbox-active {\r\n\tborder-color: #3F8DF9;\r\n}\r\n\r\n.checkbox-inner {\r\n\twidth: 24rpx;\r\n\theight: 24rpx;\r\n\tborder-radius: 50%;\r\n\tbackground-color: #3F8DF9;\r\n}\r\n\r\n.passenger-info {\r\n\tflex: 1;\r\n\tdisplay: flex;\r\n\tflex-direction: row;\r\n\talign-items: center;\r\n\tflex-wrap: nowrap;\r\n\toverflow: hidden;\r\n}\r\n\r\n.passenger-name {\r\n\tfont-size: 30rpx;\r\n\tcolor: #333;\r\n\tfont-weight: bold;\r\n\tmargin-right: 10rpx;\r\n\twhite-space: nowrap;\r\n}\r\n\r\n.passenger-id {\r\n\tfont-size: 26rpx;\r\n\tcolor: #999;\r\n\tmargin-right: 20rpx;\r\n\twhite-space: nowrap;\r\n}\r\n\r\n.passenger-type-text {\r\n\tfont-size: 24rpx;\r\n\tcolor: #666;\r\n\tbackground-color: #f5f7fa;\r\n\tpadding: 4rpx 12rpx;\r\n\tborder-radius: 4rpx;\r\n\twhite-space: nowrap;\r\n}\r\n\r\n.passenger-arrow {\r\n\twidth: 40rpx;\r\n\theight: 40rpx;\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tjustify-content: center;\r\n}\r\n\r\n.add-passenger {\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tpadding: 30rpx 0;\r\n\tjustify-content: center;\r\n\tbackground: #F7F8FA;\r\n}\r\n\r\n.add-icon {\r\n\twidth: 40rpx;\r\n\theight: 40rpx;\r\n\tmargin-right: 10rpx;\r\n}\r\n\r\n.add-text {\r\n\tfont-size: 30rpx;\r\n\tcolor: #3F8DF9;\r\n}\r\n\r\n.passenger-phone {\r\n\tdisplay: flex;\r\n\t// flex-direction: column;\r\n\tpadding: 20rpx 0;\r\n\tborder-top: 1px solid #f0f0f0;\r\n\talign-items: center;\r\n}\r\n\r\n.phone-label {\r\n\tfont-size: 28rpx;\r\n\tcolor: #303133;\r\n\tmargin-right: 10rpx;\r\n}\r\n\r\n.required {\r\n\tcolor: #ff4757;\r\n\tfont-size: 28rpx;\r\n}\r\n\r\n.phone-input {\r\n\theight: 80rpx;\r\n\tpadding: 0 20rpx;\r\n\tborder: none;\r\n\tborder-radius: 8rpx;\r\n\tfont-size: 28rpx;\r\n\tcolor: #333;\r\n\tbackground-color: #fff;\r\n}\r\n\r\n.phone-input:focus {\r\n\tborder-color: #3F8DF9;\r\n}\r\n\r\n.phone-value {\r\n\tfont-size: 28rpx;\r\n\tcolor: #333;\r\n}\r\n\r\n/* 备注信息样式 */\r\n.remark-section {\r\n\tdisplay: flex;\r\n\t// flex-direction: column;\r\n\tpadding: 20rpx 0;\r\n\tborder-top: 1px solid #f0f0f0;\r\n\talign-items: center;\r\n}\r\n\r\n.remark-label {\r\n\tfont-size: 28rpx;\r\n\tcolor: #303133;\r\n\tmargin-right: 10rpx;\r\n}\r\n\r\n.remark-input {\r\n\tpadding: 20rpx;\r\n\tborder: none;\r\n\tborder-radius: 8rpx;\r\n\tfont-size: 28rpx;\r\n\tcolor: #333;\r\n\tbackground-color: #fff;\r\n\tresize: none;\r\n\twidth: 80%;\r\n}\r\n\r\n.remark-input:focus {\r\n\tborder-color: #3F8DF9;\r\n}\r\n\r\n/* 价格信息样式 */\r\n.price-section {\r\n\tbackground-color: #fff;\r\n\tpadding: 34rpx 24rpx;\r\n\tmargin-bottom: 24rpx;\r\n\tborder-radius: 16rpx;\r\n\tmargin: 0 24rpx 24rpx 24rpx;\r\n\t// box-shadow: 0 6rpx 20rpx rgba(0, 0, 0, 0.1), 0 2rpx 5rpx rgba(0, 0, 0, 0.1);\r\n\t// border: 1rpx solid rgba(0, 0, 0, 0.03);\r\n}\r\n\r\n.price-item {\r\n\tdisplay: flex;\r\n\tjustify-content: space-between;\r\n\talign-items: center;\r\n\tpadding: 10rpx 0;\r\n}\r\n\r\n.price-label {\r\n\tfont-size: 28rpx;\r\n\tcolor: #333;\r\n}\r\n\r\n.price-value {\r\n\tfont-size: 28rpx;\r\n\tcolor: #333;\r\n}\r\n\r\n.price-discount {\r\n\tfont-size: 28rpx;\r\n\tcolor: #EE0A24;\r\n}\r\n\r\n.coupon-item {\r\n\tpadding: 20rpx 0;\r\n\tborder-top: 1px solid #f0f0f0;\r\n\tborder-bottom: 1px solid #f0f0f0;\r\n}\r\n\r\n.coupon-select {\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n}\r\n\r\n.coupon-text {\r\n\tfont-size: 28rpx;\r\n\tcolor: #999;\r\n\tmargin-right: 10rpx;\r\n}\r\n\r\n.total-price {\r\n\tdisplay: flex;\r\n\tjustify-content: space-between;\r\n\talign-items: center;\r\n\tpadding: 20rpx 0 0;\r\n}\r\n\r\n.total-label {\r\n\tfont-size: 30rpx;\r\n\tfont-weight: bold;\r\n\tcolor: #333;\r\n}\r\n\r\n.total-value {\r\n\tfont-size: 30rpx;\r\n\tfont-weight: bold;\r\n\tcolor: #333;\r\n}\r\n\r\n/* 购票须知样式 */\r\n.terms-section {\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tjustify-content: center;\r\n\tpadding: 20rpx;\r\n\tposition: fixed;\r\n\tbottom: 100rpx;\r\n\tleft: 0;\r\n\tright: 0;\r\n\tbackground-color: #fff;\r\n\tborder-top: 1rpx solid #f0f0f0;\r\n\tz-index: 9;\r\n}\r\n\r\n.terms-checkbox {\r\n\tmargin-right: 10rpx;\r\n}\r\n\r\n.terms-text {\r\n\tfont-size: 26rpx;\r\n\tcolor: #666;\r\n}\r\n\r\n.terms-link {\r\n\tfont-size: 26rpx;\r\n\tcolor: #3F8DF9;\r\n}\r\n\r\n/* 底部支付栏样式 */\r\n.payment-bar {\r\n\tposition: fixed;\r\n\tleft: 0;\r\n\tright: 0;\r\n\tbottom: 0;\r\n\tbackground-color: #fff;\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tpadding: 20rpx 30rpx;\r\n\tbox-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);\r\n\tz-index: 10;\r\n\tborder-top: 1rpx solid #f0f0f0;\r\n}\r\n\r\n.payment-total {\r\n\tflex: 1;\r\n\tdisplay: flex;\r\n\talign-items: baseline;\r\n}\r\n\r\n.payment-label {\r\n\tfont-size: 26rpx;\r\n\tcolor: #666;\r\n\tmargin-right: 10rpx;\r\n}\r\n\r\n.payment-currency {\r\n\tfont-size: 30rpx;\r\n\tcolor: #EE0A24;\r\n\tfont-weight: bold;\r\n\tmargin-right: 4rpx;\r\n}\r\n\r\n.payment-value {\r\n\tfont-size: 42rpx;\r\n\tcolor: #EE0A24;\r\n\tfont-weight: bold;\r\n}\r\n\r\n.payment-button {\r\n\tbackground-color: #3F8DF9;\r\n\tcolor: #fff;\r\n\tfont-size: 32rpx;\r\n\tpadding: 20rpx 60rpx;\r\n\tborder-radius: 50rpx;\r\n}\r\n\r\n/* 优惠券已选择状态样式 */\r\n.coupon-selected {\r\n\tfont-size: 28rpx;\r\n\tcolor: #ff9500;\r\n\tfont-weight: bold;\r\n\tmargin-right: 10rpx;\r\n}\r\n\r\n/* 优惠券选择弹窗样式 */\r\n.coupon-popup {\r\n\tposition: fixed;\r\n\tleft: 0;\r\n\tright: 0;\r\n\ttop: 0;\r\n\tbottom: 0;\r\n\tz-index: 11;\r\n}\r\n\r\n.coupon-mask {\r\n\tposition: absolute;\r\n\tleft: 0;\r\n\tright: 0;\r\n\ttop: 0;\r\n\tbottom: 0;\r\n\tbackground-color: rgba(0, 0, 0, 0.5);\r\n}\r\n\r\n.coupon-container {\r\n\tposition: fixed;\r\n\tleft: 0;\r\n\tright: 0;\r\n\tbottom: 0;\r\n\tbackground-color: #fff;\r\n\tborder-radius: 24rpx 24rpx 0 0;\r\n\tz-index: 12;\r\n\tanimation: slideUp 0.3s ease-out;\r\n}\r\n\r\n@keyframes slideUp {\r\n\tfrom {\r\n\t\ttransform: translateY(100%);\r\n\t}\r\n\tto {\r\n\t\ttransform: translateY(0);\r\n\t}\r\n}\r\n\r\n.coupon-header {\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tjustify-content: center;\r\n\tpadding: 30rpx 0;\r\n\tposition: relative;\r\n\tborder-bottom: 1px solid #f5f5f5;\r\n}\r\n\r\n.coupon-close {\r\n\tposition: absolute;\r\n\tleft: 30rpx;\r\n\tfont-size: 46rpx;\r\n\tcolor: #333;\r\n\tline-height: 1;\r\n}\r\n\r\n.coupon-title {\r\n\tfont-size: 34rpx;\r\n\tfont-weight: bold;\r\n\tcolor: #333;\r\n}\r\n\r\n.coupon-list {\r\n\tmax-height: 60vh;\r\n\toverflow-y: auto;\r\n\tpadding: 30rpx 20rpx;\r\n}\r\n\r\n.coupon-item {\r\n\tposition: relative;\r\n\tmargin-bottom: 30rpx;\r\n}\r\n\r\n.coupon-content {\r\n\tbackground-color: #FF7744;\r\n\tpadding: 30rpx 20rpx;\r\n\tborder-radius: 12rpx;\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tposition: relative;\r\n}\r\n\r\n.coupon-amount {\r\n\tdisplay: flex;\r\n\tflex-direction: column;\r\n\talign-items: center;\r\n\tjustify-content: center;\r\n\twidth: 200rpx;\r\n\tpadding-right: 20rpx;\r\n\tposition: relative;\r\n}\r\n\r\n.coupon-amount::after {\r\n\tcontent: '';\r\n\tposition: absolute;\r\n\ttop: -20rpx;\r\n\tbottom: -20rpx;\r\n\tright: 0;\r\n\twidth: 2rpx;\r\n\tbackground-color: rgba(255, 255, 255, 0.3);\r\n}\r\n\r\n.coupon-currency {\r\n\tfont-size: 36rpx;\r\n\tcolor: #fff;\r\n\tfont-weight: bold;\r\n\tmargin-right: 4rpx;\r\n}\r\n\r\n.coupon-value {\r\n\tfont-size: 80rpx;\r\n\tcolor: #fff;\r\n\tfont-weight: bold;\r\n\tline-height: 1;\r\n}\r\n\r\n.coupon-condition {\r\n\tfont-size: 24rpx;\r\n\tcolor: rgba(255, 255, 255, 0.9);\r\n\tmargin-top: 10rpx;\r\n}\r\n\r\n.coupon-info {\r\n\tflex: 1;\r\n\tpadding-left: 30rpx;\r\n\tdisplay: flex;\r\n\tflex-direction: column;\r\n\tjustify-content: center;\r\n}\r\n\r\n.coupon-type {\r\n\tfont-size: 36rpx;\r\n\tcolor: #fff;\r\n\tfont-weight: bold;\r\n\tmargin-bottom: 20rpx;\r\n}\r\n\r\n.coupon-expire {\r\n\tfont-size: 28rpx;\r\n\tcolor: rgba(255, 255, 255, 0.9);\r\n}\r\n\r\n.coupon-select-icon {\r\n\twidth: 44rpx;\r\n\theight: 44rpx;\r\n\tborder-radius: 50%;\r\n\tbackground-color: #fff;\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tjustify-content: center;\r\n\tposition: absolute;\r\n\tright: 30rpx;\r\n\ttop: 50%;\r\n\ttransform: translateY(-50%);\r\n\tborder: 2rpx solid transparent;\r\n}\r\n\r\n.selected .select-inner {\r\n\twidth: 28rpx;\r\n\theight: 28rpx;\r\n\tborder-radius: 50%;\r\n\tbackground-color: #3F8DF9;\r\n}\r\n\r\n/* 优惠券锯齿边 */\r\n.coupon-edge-left, .coupon-edge-right {\r\n\tposition: absolute;\r\n\ttop: 50%;\r\n\twidth: 30rpx;\r\n\theight: 30rpx;\r\n\tborder-radius: 50%;\r\n\tbackground-color: #f5f7fa;\r\n}\r\n\r\n.coupon-edge-left {\r\n\tleft: -15rpx;\r\n\ttransform: translateY(-50%);\r\n}\r\n\r\n.coupon-edge-right {\r\n\tright: -15rpx;\r\n\ttransform: translateY(-50%);\r\n}\r\n\r\n.coupon-footer {\r\n\tpadding: 20rpx 30rpx 50rpx;\r\n}\r\n\r\n.coupon-confirm-btn {\r\n\tbackground-color: #333;\r\n\tcolor: #fff;\r\n\tfont-size: 34rpx;\r\n\tpadding: 24rpx;\r\n\tborder-radius: 50rpx;\r\n\ttext-align: center;\r\n}\r\n</style>", "import mod from \"-!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./ticket_confirm.vue?vue&type=style&index=0&id=466fd826&lang=scss&scoped=true&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./ticket_confirm.vue?vue&type=style&index=0&id=466fd826&lang=scss&scoped=true&\""], "sourceRoot": ""}