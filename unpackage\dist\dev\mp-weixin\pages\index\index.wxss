@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
page.data-v-57280228 {
  overflow: hidden;
  height: 100%;
}
.content.data-v-57280228 {
  width: 100%;
  background-color: #ffffff;
  overflow: hidden;
  position: relative;
}
/* 顶部背景和标语 */
.header.data-v-57280228 {
  width: 100%;
  height: 400rpx;
  background: linear-gradient(to bottom, #a3c0f8, #d9e5fc);
  box-sizing: border-box;
}
.banner-image.data-v-57280228 {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
/* 选择站点 */
.station-selector.data-v-57280228 {
  width: 90%;
  margin: 20rpx auto;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.station-item.data-v-57280228 {
  flex: 1;
  display: flex;
  justify-content: center;
}
.station-name.data-v-57280228 {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}
.switch-btn.data-v-57280228 {
  width: 80rpx;
  height: 80rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}
.switch-icon.data-v-57280228 {
  width: 60rpx;
  height: 60rpx;
  transition: all 0.5s ease;
}
/* 添加旋转动画 */
.rotating.data-v-57280228 {
  -webkit-animation: rotate360-data-v-57280228 0.5s linear;
          animation: rotate360-data-v-57280228 0.5s linear;
}
@-webkit-keyframes rotate360-data-v-57280228 {
from {
    -webkit-transform: rotate(0deg);
            transform: rotate(0deg);
}
to {
    -webkit-transform: rotate(360deg);
            transform: rotate(360deg);
}
}
@keyframes rotate360-data-v-57280228 {
from {
    -webkit-transform: rotate(0deg);
            transform: rotate(0deg);
}
to {
    -webkit-transform: rotate(360deg);
            transform: rotate(360deg);
}
}
/* 日期选择 */
.date-selector.data-v-57280228 {
  width: 90%;
  margin: 20rpx auto;
  display: flex;
  align-items: center;
  height: 80rpx;
  border-bottom: 1px solid #eee;
}
.date.data-v-57280228 {
  font-size: 32rpx;
  color: #333;
  margin-right: 20rpx;
}
.week.data-v-57280228 {
  font-size: 32rpx;
  color: #333;
  margin-right: 20rpx;
}
.today.data-v-57280228 {
  font-size: 28rpx;
  color: #999;
  background-color: #f5f5f5;
  padding: 4rpx 16rpx;
  border-radius: 20rpx;
}
/* 查询按钮 */
.query-btn.data-v-57280228 {
  width: 90%;
  height: 90rpx;
  background: linear-gradient(to right, #3a97fa, #3b87f7);
  color: #fff;
  border-radius: 45rpx;
  margin: 30rpx auto;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 36rpx;
}
/* 历史记录 */
.history.data-v-57280228 {
  width: 90%;
  margin: 20rpx auto;
  display: flex;
}
.history-header.data-v-57280228 {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  padding: 10rpx 10rpx;
}
.history-clear.data-v-57280228 {
  font-size: 28rpx;
  color: #999;
}
.history-scroll.data-v-57280228 {
  width: 80%;
  white-space: nowrap;
}
.history-scroll-content.data-v-57280228 {
  display: inline-block;
  white-space: nowrap;
}
.history-item.data-v-57280228 {
  display: inline-block;
  margin-right: 20rpx;
  padding: 8rpx 20rpx;
  background-color: #f5f5f5;
  border-radius: 30rpx;
}
.history-text.data-v-57280228 {
  font-size: 28rpx;
  color: #666;
}
/* 日历弹窗 */
.calendar-popup.data-v-57280228 {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 999;
}
.calendar-mask.data-v-57280228 {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
}
.calendar-container.data-v-57280228 {
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #fff;
  border-radius: 20rpx 20rpx 0 0;
  padding: 30rpx;
}
.custom-calendar-header.data-v-57280228 {
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  margin-bottom: 20rpx;
}
.calendar-close.data-v-57280228 {
  position: absolute;
  left: 0;
  font-size: 48rpx;
  color: #333;
  line-height: 1;
}
.calendar-title.data-v-57280228 {
  font-size: 34rpx;
  font-weight: bold;
}
.calendar-footer.data-v-57280228 {
  margin-top: 20rpx;
  padding-bottom: 20rpx;
}
.calendar-confirm.data-v-57280228 {
  height: 90rpx;
  background-color: #333;
  color: #fff;
  border-radius: 45rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 32rpx;
}
/* 站点选择器弹窗 */
.station-popup.data-v-57280228 {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 999;
}
.station-mask.data-v-57280228 {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
}
.station-container.data-v-57280228 {
  position: absolute;
  top: 120rpx;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #fff;
  display: flex;
  flex-direction: column;
  border-radius: 20rpx 20rpx 0 0;
  /* 添加顶部圆角 */
  -webkit-transform: translateY(100%);
          transform: translateY(100%);
  /* 初始位置在屏幕下方 */
  transition: -webkit-transform 0.3s ease;
  transition: transform 0.3s ease;
  transition: transform 0.3s ease, -webkit-transform 0.3s ease;
  /* 添加过渡效果 */
}
.station-container-show.data-v-57280228 {
  -webkit-transform: translateY(0);
          transform: translateY(0);
  /* 显示状态 */
}
.station-header.data-v-57280228 {
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  padding: 30rpx 0;
  border-bottom: 1px solid #f0f0f0;
}
.station-close.data-v-57280228 {
  position: absolute;
  left: 30rpx;
  font-size: 48rpx;
  color: #333;
  line-height: 1;
}
.station-title.data-v-57280228 {
  font-size: 36rpx;
  font-weight: bold;
}
.station-tip.data-v-57280228 {
  background-color: #f0f8ff;
  padding: 20rpx 30rpx;
  display: flex;
  align-items: center;
}
.tip-icon.data-v-57280228 {
  color: #FFB700;
  font-size: 32rpx;
  margin-right: 10rpx;
}
.tip-text.data-v-57280228 {
  color: #666;
  font-size: 28rpx;
}
.station-content.data-v-57280228 {
  display: flex;
  flex: 1;
  overflow: hidden;
}
.city-list.data-v-57280228 {
  width: 200rpx;
  height: 100%;
  background-color: #f8f8f8;
}
.city-item.data-v-57280228 {
  padding: 30rpx 20rpx;
  text-align: center;
}
.city-item-active.data-v-57280228 {
  background-color: #fff;
  position: relative;
}
.city-item-active.data-v-57280228::before {
  content: "";
  position: absolute;
  left: 0;
  top: 30%;
  height: 40%;
  width: 8rpx;
  background-color: #3F8DF9;
}
.city-name.data-v-57280228 {
  font-size: 28rpx;
  color: #333;
}
.place-list.data-v-57280228 {
  flex: 1;
  height: 100%;
  padding: 0 30rpx;
}
.place-item.data-v-57280228 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx 0;
  border-bottom: 1px solid #f0f0f0;
}
.place-name.data-v-57280228 {
  font-size: 32rpx;
  color: #333;
}
.place-item-selected .place-name.data-v-57280228 {
  color: #3F8DF9;
}
.place-check.data-v-57280228 {
  color: #3F8DF9;
  font-size: 32rpx;
  font-weight: bold;
}
.station-footer.data-v-57280228 {
  padding: 20rpx 30rpx 40rpx;
}
.station-confirm.data-v-57280228 {
  height: 90rpx;
  background-color: #333;
  color: #fff;
  border-radius: 45rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 32rpx;
}
/* 自定义uni-calendar样式 */
.custom-calendar.data-v-57280228 {
  --calendar-border-color: #f5f5f5;
  --calendar-text-color: #333;
  --calendar-lunar-color: #999;
  --calendar-background-color: #fff;
  --calendar-selected-background-color: #3F8DF9;
  --calendar-selected-lunar-color: #fff;
  --calendar-selected-text-color: #fff;
}
/* uni-calendar组件样式修改 */
.data-v-57280228:deep(.uni-calendar) {
  background-color: #ffffff;
}
.data-v-57280228:deep(.uni-calendar__header) {
  display: none !important;
}
.data-v-57280228:deep(.uni-calendar__weeks) {
  padding: 10rpx 0;
}
.data-v-57280228:deep(.uni-calendar__weeks-day) {
  height: 90rpx;
}
.data-v-57280228:deep(.uni-calendar__weeks-day-text) {
  font-size: 30rpx;
  color: #333;
}
.data-v-57280228:deep(.uni-calendar__selected) {
  background-color: #3F8DF9;
  color: #fff;
  border-radius: 50%;
  width: 70rpx;
  height: 70rpx;
  line-height: 70rpx;
  text-align: center;
}
.data-v-57280228:deep(.uni-calendar__disabled) {
  color: #ccc;
  cursor: default;
}
.data-v-57280228:deep(.uni-calendar-item--disable) {
  color: #ccc;
  cursor: default;
}
.data-v-57280228:deep(.uni-calendar-item--before-checked),.data-v-57280228:deep(.uni-calendar-item--after-checked) {
  background-color: rgba(63, 141, 249, 0.1);
  color: #333;
}
.data-v-57280228:deep(.uni-calendar__week-day) {
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  font-size: 28rpx;
  color: #333;
}
.data-v-57280228:deep(.uni-calendar__week-day-text) {
  color: #333;
}
.data-v-57280228:deep(.uni-calendar__weeks-day-text) {
  color: #333;
}
.data-v-57280228:deep(.uni-calendar-item__weeks-box-circle) {
  border: 2rpx solid #3F8DF9;
  color: #3F8DF9;
  border-radius: 50%;
}
.no-place.data-v-57280228 {
  padding: 50rpx 0;
  text-align: center;
}
.no-place-text.data-v-57280228 {
  font-size: 28rpx;
  color: #999;
}
