(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["subpkg-booking/ticket_confirm/ticket_confirm"],{80:function(e,t,n){"use strict";(function(e,t){var r=n(4);n(26);r(n(25));var o=r(n(81));e.__webpack_require_UNI_MP_PLUGIN__=n,t(o.default)}).call(this,n(1)["default"],n(2)["createPage"])},81:function(e,t,n){"use strict";n.r(t);var r=n(82),o=n(84);for(var i in o)["default"].indexOf(i)<0&&function(e){n.d(t,e,(function(){return o[e]}))}(i);n(86);var a,c=n(33),s=Object(c["default"])(o["default"],r["render"],r["staticRenderFns"],!1,null,"466fd826",null,!1,r["components"],a);s.options.__file="subpkg-booking/ticket_confirm/ticket_confirm.vue",t["default"]=s.exports},82:function(e,t,n){"use strict";n.r(t);var r=n(83);n.d(t,"render",(function(){return r["render"]})),n.d(t,"staticRenderFns",(function(){return r["staticRenderFns"]})),n.d(t,"recyclableRender",(function(){return r["recyclableRender"]})),n.d(t,"components",(function(){return r["components"]}))},83:function(e,t,n){"use strict";var r;n.r(t),n.d(t,"render",(function(){return o})),n.d(t,"staticRenderFns",(function(){return a})),n.d(t,"recyclableRender",(function(){return i})),n.d(t,"components",(function(){return r}));var o=function(){var e=this,t=e.$createElement,n=(e._self._c,e.totalPrice.toFixed(2)),r=e.discount.toFixed(2),o=e.selectedCoupon?e.couponDiscount.toFixed(2):null,i=e.finalPrice.toFixed(2),a=e.finalPrice.toFixed(2);e.$mp.data=Object.assign({},{$root:{g0:n,g1:r,g2:o,g3:i,g4:a}})},i=!1,a=[];o._withStripped=!0},84:function(e,t,n){"use strict";n.r(t);var r=n(85),o=n.n(r);for(var i in r)["default"].indexOf(i)<0&&function(e){n.d(t,e,(function(){return r[e]}))}(i);t["default"]=o.a},85:function(e,t,n){"use strict";(function(e){var r=n(4);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var o=r(n(58)),i=r(n(11)),a=r(n(60)),c=n(61),s=r(n(30)),u=r(n(41));function l(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function d(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?l(Object(n),!0).forEach((function(t){(0,i.default)(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):l(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var p={data:function(){return{ticketId:null,loading:!1,loadingText:"加载中...",departure:"",departureDoor:"",arrival:"",date:"",dateDesc:"",time:"",duration:"",typeName:"",type:"",departureLocation:"",originalPrice:0,currentPrice:0,discount:0,finalPrice:0,passengerList:[],contactPhone:"",remark:"",totalPrice:100,termsAgreed:!1,showCouponPopup:!1,couponList:[],selectedCoupon:null,couponDiscount:0}},onLoad:function(t){this.loadUserContactInfo(),this.loadCouponList(),t&&t.ticketId?(this.ticketId=t.ticketId,this.departure=decodeURIComponent(t.departure||""),this.arrival=decodeURIComponent(t.arrival||""),this.date=decodeURIComponent(t.date||""),this.dateDesc=decodeURIComponent(t.dateDesc||""),this.departure&&this.arrival&&e.setNavigationBarTitle({title:"".concat(this.departure," — ").concat(this.arrival)}),this.loadTicketDetail()):this.loadBackupData(t)},methods:{loadUserContactInfo:function(){try{var e=s.default.getUserInfo();e&&e.phone?(this.contactPhone=e.phone,console.log("从缓存预填充用户手机号:",this.contactPhone)):(console.warn("缓存中未找到用户手机号信息，需要用户手动输入"),this.contactPhone="")}catch(t){console.error("获取用户联系信息失败:",t),this.contactPhone=""}},loadCouponList:function(){var e=this;return(0,a.default)(o.default.mark((function t(){var n;return o.default.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.prev=0,console.log("开始加载优惠券列表..."),t.next=4,u.default.get("/app/coupon/user/list");case 4:n=t.sent,n&&200===n.code&&n.data?(e.couponList=e.processCouponsData(n.data),console.log("优惠券列表加载成功:",e.couponList)):(console.warn("优惠券列表加载失败:",n),e.couponList=[]),t.next=12;break;case 8:t.prev=8,t.t0=t["catch"](0),console.error("加载优惠券列表失败:",t.t0),e.couponList=[];case 12:case"end":return t.stop()}}),t,null,[[0,8]])})))()},processCouponsData:function(e){var t=this;return e.filter((function(e){return 0===e.type})).map((function(e){var n=t.formatDate(e.endTime);return{id:e.id,amount:e.balancePice,title:e.couponName||"优惠券",type:"无门槛",expireDate:n,selected:!1,condition:"",couponId:e.couponId}}))},formatDate:function(e){if(!e)return"";try{var t=new Date(e),n=t.getFullYear(),r=String(t.getMonth()+1).padStart(2,"0"),o=String(t.getDate()).padStart(2,"0"),i=String(t.getHours()).padStart(2,"0"),a=String(t.getMinutes()).padStart(2,"0");return"".concat(n,".").concat(r,".").concat(o," ").concat(i,":").concat(a)}catch(c){return console.error("日期格式化失败:",c),e}},calculatePriceFromApi:function(){var e=this;return(0,a.default)(o.default.mark((function t(){var n,r,i,a;return o.default.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(t.prev=0,n=e.passengerList.filter((function(e){return e.selected})).length,0!==n){t.next=8;break}return console.log("没有选中的乘客，跳过价格计算"),e.totalPrice=0,e.discount=0,e.finalPrice=0,t.abrupt("return");case 8:return console.log("selectedCoupon",e.selectedCoupon),r={ticketId:e.ticketId,number:n,couponId:e.selectedCoupon?e.selectedCoupon.couponId:null},console.log("调用计算价格接口，参数:",r),t.next=13,u.default.post("/app/order/calculate/price",r);case 13:i=t.sent,i&&200===i.code&&i.data?(console.log("价格计算接口响应:",i),a=i.data,e.totalPrice=a.originalPrice||0,e.discount=a.discountPrice||0,e.finalPrice=a.number||0,e.couponDiscount=e.totalPrice-e.discount-e.finalPrice,e.couponDiscount<0&&(e.couponDiscount=0),console.log("价格更新完成:",{totalPrice:e.totalPrice,discount:e.discount,couponDiscount:e.couponDiscount,finalPrice:e.finalPrice})):console.warn("价格计算接口调用失败:",i),t.next=20;break;case 17:t.prev=17,t.t0=t["catch"](0),console.error("调用价格计算接口失败:",t.t0);case 20:case"end":return t.stop()}}),t,null,[[0,17]])})))()},loadTicketDetail:function(){var t=this;return(0,a.default)(o.default.mark((function n(){var r;return o.default.wrap((function(n){while(1)switch(n.prev=n.next){case 0:return n.prev=0,t.loading=!0,t.loadingText="正在加载车票信息...",console.log("获取车票详情，ID:",t.ticketId),n.next=6,c.ticketApi.getTicketById(t.ticketId);case 6:if(r=n.sent,!r||!r.data){n.next=11;break}t.processTicketDetail(r.data),n.next=12;break;case 11:throw new Error("获取车票信息失败");case 12:n.next=19;break;case 14:n.prev=14,n.t0=n["catch"](0),console.error("获取车票详情失败:",n.t0),e.showToast({title:"获取车票信息失败",icon:"none"}),t.loadBackupDataFromUrl();case 19:return n.prev=19,t.loading=!1,n.finish(19);case 22:case"end":return n.stop()}}),n,null,[[0,14,19,22]])})))()},processTicketDetail:function(e){this.time=this.extractTimeFromDateTime(e.departureTime),this.typeName=e.carType,this.type=this.getTicketType(e.vehicleType),this.departureDoor=e.departureDoor,this.duration=e.travelTime,this.originalPrice=parseFloat(e.originalPrice||0),this.currentPrice=parseFloat(e.currentPrice||e.price||0),this.discount=parseFloat(e.discount||0),this.departureLocation=e.departureLocation,this.calculatePrice(),console.log("车票详情处理完成:",{time:this.time,typeName:this.typeName,originalPrice:this.originalPrice,currentPrice:this.currentPrice})},extractTimeFromDateTime:function(e){if(!e)return"08:00";try{var t="";if(e.includes(" "))t=e.split(" ")[1];else{if(!e.includes("T"))return e;t=e.split("T")[1]}return t&&t.length>=5?t.substring(0,5):"08:00"}catch(n){return console.error("解析发车时间失败:",e,n),"08:00"}},getTicketType:function(e){switch(e){case 1:case"商务车":return"business";case 2:case"普通车":default:return"normal"}},loadBackupData:function(t){var n={departure:"同济大学",departureDoor:"南门",arrival:"泰安各县城",date:"05月14日",dateDesc:"明天",time:"07:50",duration:"约3小时",typeName:"商务车",type:"business",originalPrice:100,currentPrice:98,discount:2};t&&Object.keys(t).length>0?(this.departure=decodeURIComponent(t.departure||n.departure),this.departureDoor=decodeURIComponent(t.departureDoor||n.departureDoor),this.arrival=decodeURIComponent(t.arrival||n.arrival),this.date=decodeURIComponent(t.date||n.date),this.dateDesc=decodeURIComponent(t.dateDesc||n.dateDesc),this.time=decodeURIComponent(t.time||n.time),this.duration=decodeURIComponent(t.duration||n.duration),this.typeName=decodeURIComponent(t.typeName||n.typeName),this.type=t.type||n.type,this.originalPrice=parseFloat(t.originalPrice)||n.originalPrice,this.currentPrice=parseFloat(t.currentPrice)||n.currentPrice,this.discount=parseFloat(t.discount)||n.discount):Object.assign(this,n),e.setNavigationBarTitle({title:"".concat(this.departure," — ").concat(this.arrival)}),this.calculatePrice()},loadBackupDataFromUrl:function(){var e=getCurrentPages(),t=e[e.length-1],n=t.options;n&&(this.departureDoor=decodeURIComponent(n.departureDoor||"南门"),this.time=decodeURIComponent(n.time||"08:00"),this.duration=decodeURIComponent(n.duration||"约6小时"),this.typeName=decodeURIComponent(n.typeName||"普通车"),this.type=n.type||"normal",this.originalPrice=parseFloat(n.originalPrice)||100,this.currentPrice=parseFloat(n.currentPrice)||98,this.discount=parseFloat(n.discount)||2,this.calculatePrice())},showAddressDetail:function(){},selectPassenger:function(e){this.passengerList.length>0?(this.passengerList[e].selected=!this.passengerList[e].selected,this.calculatePrice(),this.calculatePriceFromApi()):this.goToPassengerManagement()},goToPassengerManagement:function(){e.navigateTo({url:"/subpkg-user/passenger_management/passenger_management"})},editPassenger:function(t){var n=this.passengerList[t];e.navigateTo({url:"/subpkg-user/passenger_edit/passenger_edit",success:function(e){e.eventChannel.emit("acceptPassengerData",{index:t,passenger:n})}})},addPassenger:function(){e.navigateTo({url:"/subpkg-user/passenger_edit/passenger_edit"})},addNewPassenger:function(e){this.passengerList.push(e),this.calculatePrice(),this.calculatePriceFromApi()},updatePassenger:function(e,t){e>=0&&e<this.passengerList.length&&(this.passengerList[e]=t,this.calculatePrice(),this.calculatePriceFromApi())},removePassenger:function(e){e>=0&&e<this.passengerList.length&&(this.passengerList.splice(e,1),this.calculatePrice(),this.calculatePriceFromApi())},receiveSelectedPassengers:function(t){var n=this;t.forEach((function(e){var t=n.passengerList.some((function(t){return t.id===e.id||t.idCard===e.idCard}));t||n.passengerList.push(d(d({},e),{},{selected:!0,type:"成人票"}))})),this.calculatePrice(),this.calculatePriceFromApi(),e.showToast({title:"已选择".concat(t.length,"位乘客"),icon:"success"})},selectCoupon:function(){this.showCouponPopup=!0,this.selectedCoupon=this.couponList.find((function(e){return e.selected}))||null},closeCouponPopup:function(){this.showCouponPopup=!1},chooseCoupon:function(e){var t=this.couponList[e];t.selected?(t.selected=!1,this.selectedCoupon=null):(this.couponList.forEach((function(e){e.selected=!1})),t.selected=!0,this.selectedCoupon=t),console.log("选择的优惠券:",this.selectedCoupon)},confirmCoupon:function(){this.selectedCoupon||(this.couponDiscount=0),this.calculatePriceFromApi(),this.closeCouponPopup()},toggleTerms:function(){this.termsAgreed=!this.termsAgreed},viewTerms:function(){return(0,a.default)(o.default.mark((function t(){var n;return o.default.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.prev=0,e.showLoading({title:"加载中..."}),t.next=4,c.configApi.getConfig("bay_ticket");case 4:n=t.sent,e.hideLoading(),n&&200===n.code?e.showModal({title:"购票须知",content:n.msg||"暂无内容",showCancel:!1,confirmText:"我知道了"}):e.showToast({title:"获取购票须知失败",icon:"none"}),t.next=14;break;case 9:t.prev=9,t.t0=t["catch"](0),e.hideLoading(),console.error("获取购票须知失败:",t.t0),e.showToast({title:"获取购票须知失败",icon:"none"});case 14:case"end":return t.stop()}}),t,null,[[0,9]])})))()},confirmPayment:function(){var t=this;return(0,a.default)(o.default.mark((function n(){var r,i,a,c;return o.default.wrap((function(n){while(1)switch(n.prev=n.next){case 0:if(t.termsAgreed){n.next=3;break}return e.showToast({title:"请阅读并同意《购票须知》",icon:"none"}),n.abrupt("return");case 3:if(r=t.passengerList.filter((function(e){return e.selected})),0!==r.length){n.next=7;break}return e.showToast({title:"请选择乘车人",icon:"none"}),n.abrupt("return");case 7:if(t.contactPhone&&""!==t.contactPhone.trim()){n.next=10;break}return e.showToast({title:"请输入联系电话",icon:"none"}),n.abrupt("return");case 10:if(i=/^1[3-9]\d{9}$/,i.test(t.contactPhone)){n.next=14;break}return e.showToast({title:"请输入正确的手机号码",icon:"none"}),n.abrupt("return");case 14:return n.prev=14,e.showLoading({title:"正在创建订单...",mask:!0}),a={ticketId:t.ticketId,couponId:t.selectedCoupon?t.selectedCoupon.id:null,phone:parseInt(t.contactPhone),remark:t.remark||"",sysPassengerInformations:r.map((function(e){return{id:e.id,name:e.name,idNumber:e.rawIdNumber||e.idCard}}))},console.log("创建订单请求参数:",a),n.next=20,u.default.post("/app/order",a);case 20:if(c=n.sent,!c||200!==c.code){n.next=26;break}console.log("订单创建成功:",c),c.data&&c.data.appid?(e.showLoading({title:"正在发起支付...",mask:!0}),t.callWechatPayWithParams(c.data)):(e.hideLoading(),e.showToast({title:"订单创建成功",icon:"success"})),n.next=27;break;case 26:throw new Error(c.msg||"订单创建失败");case 27:n.next=34;break;case 29:n.prev=29,n.t0=n["catch"](14),e.hideLoading(),console.error("订单创建失败:",n.t0),e.showToast({title:n.t0.message||"订单创建失败，请重试",icon:"none"});case 34:case"end":return n.stop()}}),n,null,[[14,29]])})))()},callWechatPayWithParams:function(t){e.hideLoading(),console.log("调用微信支付，参数:",t);var n={provider:"wxpay",appid:t.appid,timeStamp:t.timeStamp,nonceStr:t.nonceStr,package:t.prepayId,signType:t.signType,paySign:t.paySign};console.log("微信支付参数:",n),e.requestPayment(d(d({},n),{},{success:function(t){console.log("支付成功:",t),e.showToast({title:"支付成功",icon:"success"}),setTimeout((function(){e.navigateTo({url:"/pages/record/record?paymentSuccess=true"})}),1500)},fail:function(t){console.error("支付失败:",t);var n="支付失败，请重试";t.errMsg&&(t.errMsg.includes("cancel")?n="支付已取消":t.errMsg.includes("fail")&&(n="支付失败，请检查网络或重试")),e.showToast({title:n,icon:"none"})}}))},callWechatPay:function(t){var n=Math.floor(Date.now()/1e3).toString(),r=this.generateNonceStr(),o="wx01c1e1d1bfcd4145";e.hideLoading(),console.log("调用微信支付，参数:",{appId:o,timeStamp:n,nonceStr:r,package:t,signType:"MD5"}),e.requestPayment({provider:"wxpay",appid:"",timeStamp:"",nonceStr:"",package:"",signType:"",paySign:"",success:function(t){console.log("支付成功:",t),e.showToast({title:"支付成功",icon:"success"}),setTimeout((function(){e.reLaunch({url:"/pages/record/record?paymentSuccess=true"})}),1500)},fail:function(t){console.error("支付失败:",t);var n="支付失败，请重试";t.errMsg&&(t.errMsg.includes("cancel")?n="支付已取消":t.errMsg.includes("fail")&&(n="支付失败，请检查网络或重试")),e.showToast({title:n,icon:"none"})}})},generateNonceStr:function(){for(var e="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789",t="",n=0;n<32;n++)t+=e.charAt(Math.floor(Math.random()*e.length));return t},calculatePrice:function(){var e=this.passengerList.filter((function(e){return e.selected})).length;if(0===e)return this.totalPrice=0,this.discount=0,this.couponDiscount=0,void(this.finalPrice=0);this.totalPrice=this.originalPrice*e,this.discount=2*e;var t=this.totalPrice-this.discount-this.couponDiscount;this.finalPrice=t>0?t:0}}};t.default=p}).call(this,n(2)["default"])},86:function(e,t,n){"use strict";n.r(t);var r=n(87),o=n.n(r);for(var i in r)["default"].indexOf(i)<0&&function(e){n.d(t,e,(function(){return r[e]}))}(i);t["default"]=o.a},87:function(e,t,n){}},[[80,"common/runtime","common/vendor"]]]);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/subpkg-booking/ticket_confirm/ticket_confirm.js.map