/**
 * 封装请求方法
 * 统一处理请求，添加token，处理错误等
 */

// 基础URL，根据实际环境配置
const BASE_URL = 'http://192.168.2.104:8080';
// const BASE_URL = 'https://api.luhangxiaoyuan.icu';

// 请求封装
const request = (options) => {
  return new Promise((resolve, reject) => {
    // 获取本地存储的token
    const token = uni.getStorageSync('token');
    
    // 确保URL正确拼接
    let url = options.url;
    // 如果不是完整URL，并且不是以/开头，添加/
    if (url.indexOf('http') !== 0 && url.charAt(0) !== '/') {
      url = '/' + url;
    }
    
    // 确保BASE_URL和路径之间只有一个斜杠
    const baseUrl = BASE_URL.endsWith('/') ? BASE_URL.slice(0, -1) : BASE_URL;
    const fullUrl = baseUrl + url;
    
    // 请求默认参数
    const defaultOptions = {
      url: fullUrl,
      timeout: 10000,
      header: {
        'content-type': 'application/json'
      },
      data: options.data || {},
      method: options.method || 'GET'
    };
    
    console.log('发送请求到:', fullUrl);
    
    // 如果有token，添加到请求头
    if (token) {
      defaultOptions.header['Authorization'] = 'Bearer ' + token;
    }
    
    // 发起请求
    uni.request({
      ...defaultOptions,
      success: (res) => {
        console.log(res);
        
        // 判断请求是否成功（这里根据后端接口规范调整）
        if (res.statusCode === 200) {
          // 判断业务状态码（根据实际后端接口规范调整）
          if (res.data.code === 200) {
            resolve(res.data);
          } else if (res.data.code === 401) {
            // token失效或未登录
            uni.showToast({
              title: '登录已过期，请重新登录',
              icon: 'none'
            });
            // 清除本地token
            uni.removeStorageSync('token');
            // 跳转登录页
            setTimeout(() => {
              uni.navigateTo({
                url: '/pages/login/login'
              });
            }, 500);
            reject(res.data);
          } else {
            // 其他业务错误
            uni.showToast({
              title: res.data.msg || '服务器开小差了',
              icon: 'none'
            });
            reject(res.data);
          }
        } else {
          // HTTP状态码错误
          uni.showToast({
            title: '网络请求失败',
            icon: 'none'
          });
          reject(res);
        }
      },
      fail: (err) => {
        // 请求失败
        console.error('请求失败:', {
          url: fullUrl,
          method: options.method,
          data: options.data,
          error: err
        });
        
        uni.showToast({
          title: '网络连接失败',
          icon: 'none'
        });
        reject(err);
      }
    });
  });
};

// 封装GET请求
const get = (url, data = {}) => {
  return request({
    url,
    data,
    method: 'GET'
  });
};

// 封装POST请求
const post = (url, data = {}) => {
  return request({
    url,
    data,
    method: 'POST'
  });
};

// 封装PUT请求
const put = (url, data = {}) => {
  return request({
    url,
    data,
    method: 'PUT'
  });
};

// 封装DELETE请求
const del = (url, data = {}) => {
  return request({
    url,
    data,
    method: 'DELETE'
  });
};

// 封装文件上传请求
const upload = (filePath, url = '/common/file/upload', name = 'file', formData = {}) => {
  return new Promise((resolve, reject) => {
    // 获取本地存储的token
    const token = uni.getStorageSync('token');

    // 确保URL正确拼接
    let uploadUrl = url;
    if (uploadUrl.indexOf('http') !== 0 && uploadUrl.charAt(0) !== '/') {
      uploadUrl = '/' + uploadUrl;
    }

    const baseUrl = BASE_URL.endsWith('/') ? BASE_URL.slice(0, -1) : BASE_URL;
    const fullUrl = baseUrl + uploadUrl;

    console.log('上传文件到:', fullUrl);

    // 构建请求头
    const header = {};
    if (token) {
      header['Authorization'] = 'Bearer ' + token;
    }

    // 发起文件上传请求
    uni.uploadFile({
      url: fullUrl,
      filePath: filePath,
      name: name,
      formData: formData,
      header: header,
      success: (res) => {
        console.log('文件上传响应:', res);

        try {
          // 解析响应数据
          const data = JSON.parse(res.data);

          if (res.statusCode === 200) {
            if (data.code === 200) {
              resolve(data);
            } else if (data.code === 401) {
              // token失效或未登录
              uni.showToast({
                title: '登录已过期，请重新登录',
                icon: 'none'
              });
              uni.removeStorageSync('token');
              setTimeout(() => {
                uni.navigateTo({
                  url: '/pages/login/login'
                });
              }, 1500);
              reject(data);
            } else {
              uni.showToast({
                title: data.msg || '上传失败',
                icon: 'none'
              });
              reject(data);
            }
          } else {
            uni.showToast({
              title: '上传失败',
              icon: 'none'
            });
            reject(res);
          }
        } catch (e) {
          console.error('解析上传响应失败:', e);
          uni.showToast({
            title: '上传失败',
            icon: 'none'
          });
          reject(e);
        }
      },
      fail: (err) => {
        console.error('文件上传失败:', err);
        uni.showToast({
          title: '上传失败',
          icon: 'none'
        });
        reject(err);
      }
    });
  });
};

// 导出请求方法
export default {
  request,
  get,
  post,
  put,
  del,
  upload
};